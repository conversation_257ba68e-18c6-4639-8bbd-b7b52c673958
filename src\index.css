@import url('https://fonts.googleapis.com/css2?family=Fira+Code:wght@300;400;500;600;700&display=swap');
@import url('https://api.fontshare.com/v2/css?f[]=satoshi@400,500,700,900,1,2&display=swap');

@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 0 0% 0%;
    --foreground: 0 0% 100%;

    --card: 0 0% 4%;
    --card-foreground: 0 0% 100%;

    --popover: 0 0% 4%;
    --popover-foreground: 0 0% 100%;

    --primary: 0 0% 100%;
    --primary-foreground: 0 0% 0%;

    --secondary: 0 0% 10%;
    --secondary-foreground: 0 0% 100%;

    --muted: 0 0% 15%;
    --muted-foreground: 0 0% 70%;

    --accent: 0 0% 15%;
    --accent-foreground: 0 0% 100%;

    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 0 0% 100%;

    --border: 0 0% 20%;
    --input: 0 0% 20%;
    --ring: 0 0% 70%;

    --radius: 0.5rem;
    
    --neon-green: 154 100% 50%; /* #00FF9D */
  }
}

@layer base {
  * {
    @apply border-border;
  }

  html {
    @apply scroll-smooth;
    scroll-behavior: smooth;
  }

  body {
    @apply bg-background text-foreground font-satoshi overscroll-none;
  }

  h1, h2, h3, h4, h5, h6 {
    @apply font-fira font-medium tracking-tight;
  }

  p {
    @apply font-satoshi;
  }
}

@layer components {
  .neon-text {
    @apply relative inline-block text-white;
  }
  
  .neon-text::after {
    content: attr(data-text);
    @apply absolute left-0 top-0 w-full h-full text-transparent z-10 animate-neon-glow;
  }
  
  .neon-border {
    @apply relative border border-transparent;
  }
  
  .neon-border::after {
    content: "";
    @apply absolute inset-0 z-[-1] border-2 border-white rounded-md animate-neon-glow;
  }
  
  .custom-cursor {
    @apply fixed w-6 h-6 rounded-full pointer-events-none z-[999] mix-blend-difference bg-white transform -translate-x-1/2 -translate-y-1/2 transition-transform duration-100;
  }
  
  .grid-bg {
    @apply bg-black bg-grid-pattern bg-[size:40px_40px];
  }
  
  .neon-button {
    @apply relative inline-flex items-center justify-center px-6 py-3 overflow-hidden font-fira font-medium text-white border border-white rounded-md hover:scale-105 transition-transform;
  }
  
  .neon-button:hover {
    @apply animate-neon-glow border-neon-green text-neon-green;
  }
  
  .bento-card {
    @apply relative p-6 bg-card rounded-xl border border-border overflow-hidden transition-all duration-300 hover:-translate-y-1;
  }
  
  .bento-card::before {
    content: "";
    @apply absolute inset-0 opacity-0 transition-opacity duration-300 bg-gradient-to-r from-transparent via-neon-green/10 to-transparent;
  }
  
  .bento-card:hover::before {
    @apply opacity-100;
  }
  
  .process-item {
    @apply relative pl-8 pb-10;
  }
  
  .process-item::before {
    content: "";
    @apply absolute left-0 top-2 w-3 h-3 rounded-full bg-neon-green animate-neon-glow;
  }
  
  .process-item::after {
    content: "";
    @apply absolute left-[6px] top-5 w-[1px] h-full bg-gradient-to-b from-neon-green/50 to-transparent;
  }
  
  .process-item:last-child::after {
    @apply hidden;
  }

  .nav-link {
    @apply relative py-2 after:absolute after:left-0 after:bottom-0 after:w-0 after:h-0.5 after:bg-current after:transition-all hover:after:w-full;
  }
  
  /* Animations for infinite scroll */
  @keyframes infinite-scroll {
    from {
      transform: translateX(0);
    }
    to {
      transform: translateX(-50%);
    }
  }
  
  .animate-infinite-scroll {
    animation: infinite-scroll 30s linear infinite;
  }
  
  /* Opposite direction for second row */
  @keyframes infinite-scroll-reverse {
    from {
      transform: translateX(-50%);
    }
    to {
      transform: translateX(0);
    }
  }
  
  .animate-infinite-scroll-reverse {
    animation: infinite-scroll-reverse 30s linear infinite;
  }
  
  .futuristic-nav-link {
    @apply relative py-2 px-1 overflow-hidden transition-all duration-300;
  }
  
  .futuristic-nav-link::before {
    content: "";
    @apply absolute bottom-0 left-0 w-0 h-[2px] bg-neon-green transition-all duration-500;
  }
  
  .futuristic-nav-link:hover::before,
  .active-nav-link::before {
    @apply w-full;
  }
  
  .futuristic-nav-link::after {
    content: attr(data-text);
    @apply absolute left-0 top-0 w-full h-full flex items-center justify-center opacity-0 text-neon-green transition-all duration-500 scale-110;
  }
  
  .futuristic-nav-link:hover::after {
    @apply opacity-100 scale-100;
  }
  
  .schedule-meeting-btn {
    position: relative;
    overflow: hidden;
  }
  
  .schedule-meeting-btn::before {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: conic-gradient(transparent, rgba(0, 255, 157, 0.2), transparent 30%);
    animation: rotate 4s linear infinite;
  }
  
  .schedule-meeting-btn::after {
    content: '';
    position: absolute;
    inset: 1px;
    background: rgba(0, 0, 0, 0.8);
    border-radius: 0.375rem;
    transition: background-color 0.3s ease;
  }
  
  .schedule-meeting-btn:hover::after {
    background: rgba(0, 255, 157, 0.2);
  }

  .get-in-touch-btn {
    position: relative;
    overflow: hidden;
  }
  
  .get-in-touch-btn::before {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: conic-gradient(transparent, rgba(0, 255, 157, 0.2), transparent 30%);
    animation: rotate 4s linear infinite;
  }
  
  .get-in-touch-btn::after {
    content: '';
    position: absolute;
    inset: 1px;
    background: rgba(0, 0, 0, 0.8);
    border-radius: 0.375rem;
    transition: background-color 0.3s ease;
  }
  
  .get-in-touch-btn:hover::after {
    background: rgba(0, 255, 157, 0.2);
  }
  
  @keyframes rotate {
    100% {
      transform: rotate(1turn);
    }
  }
  
  .code-container {
    position: relative;
    overflow: hidden;
  }
  
  .code-container::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(0, 255, 157, 0.2), transparent);
    transform: translateX(-100%);
    animation: code-scan 3s linear infinite;
  }
  
  @keyframes code-scan {
    100% {
      transform: translateX(100%);
    }
  }
  
  /* Project card enhancement */
  .project-card {
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
    transition: all 0.3s ease;
  }
  
  .project-card:hover {
    box-shadow: 0 8px 30px rgba(0, 0, 0, 0.5), 0 0 25px rgba(0, 255, 157, 0.3);
    transform: translateY(-5px);
  }
  
  /* Scroll reveal animations */
  .reveal-section {
    position: relative;
  }
  
  .reveal-item {
    opacity: 0;
    transform: translateY(30px);
    transition: opacity 0.8s ease, transform 0.8s ease;
  }
  
  .reveal-item.revealed {
    opacity: 1;
    transform: translateY(0);
  }
  
  /* Transition delay for multiple items */
  .reveal-item:nth-child(2) {
    transition-delay: 0.2s;
  }
  
  .reveal-item:nth-child(3) {
    transition-delay: 0.4s;
  }
  
  .reveal-item:nth-child(4) {
    transition-delay: 0.6s;
  }
  
  .reveal-item:nth-child(5) {
    transition-delay: 0.8s;
  }
  
  /* Enhanced futuristic navigation hover effects */
  .futuristic-nav-link {
    @apply relative py-2 px-1 overflow-hidden transition-all duration-300;
  }
  
  .futuristic-nav-link::before {
    content: "";
    @apply absolute bottom-0 left-0 w-0 h-[2px] bg-neon-green transition-all duration-500;
  }
  
  .futuristic-nav-link:hover::before,
  .active-nav-link::before {
    @apply w-full;
  }
  
  .futuristic-nav-link::after {
    content: attr(data-text);
    @apply absolute left-0 top-0 w-full h-full flex items-center justify-center opacity-0 text-neon-green transition-all duration-500 scale-110;
  }
  
  .futuristic-nav-link:hover::after {
    @apply opacity-100 scale-100;
  }
  
  /* Project row scrollers */
  .project-row-scroller {
    display: flex;
    flex-wrap: nowrap;
    gap: 16px;
  }
  
  /* Testimonial slider */
  .testimonial-slider-container {
    position: relative;
    width: 100%;
    overflow: hidden;
    padding: 10px 0;
  }
  
  .testimonial-slider {
    display: flex;
    flex-wrap: nowrap;
  }
  
  /* News ticker */
  .news-ticker {
    position: relative;
    display: flex;
    overflow: hidden;
    width: 100%;
  }
  
  /* Glass morphism effect */
  .glass-morphism {
    @apply backdrop-blur-md bg-black/40 border border-neon-green/20 shadow-lg;
  }
  
  /* Mobile responsiveness for project grid */
  @media (max-width: 640px) {
    .project-card {
      width: 150px;
      height: 112.5px; /* 4:3 ratio */
    }
  }
}

/* Custom Cursor */
* {
  cursor: none;
}

/* Hide default scrollbar */
::-webkit-scrollbar {
  width: 6px;
}

::-webkit-scrollbar-track {
  background: #000;
}

::-webkit-scrollbar-thumb {
  background: #444;
  border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
  background: #666;
}

/* Fix mobile cursor */
@media (max-width: 640px) {
  * {
    cursor: auto;
  }
  
  .custom-cursor {
    display: none;
  }
}
