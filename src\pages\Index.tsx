
import React, { useEffect } from 'react';
import CustomCursor from '@/components/CustomCursor';
import Header from '@/components/Header';
import HeroSection from '@/components/HeroSection';
import AboutSection from '@/components/AboutSection';
import ServicesSection from '@/components/ServicesSection';
import ProjectsSection from '@/components/ProjectsSection';
import ProcessSection from '@/components/ProcessSection';
import IndustriesSection from '@/components/IndustriesSection';
import TestimonialsSection from '@/components/TestimonialsSection';
import TeamSection from '@/components/TeamSection';
import DinoGame from '@/components/DinoGame';
import ContactSection from '@/components/ContactSection';
import Footer from '@/components/Footer';


const Index = () => {
  useEffect(() => {
    // Update document title
    document.title = 'Nerdlab | We Build The Future With Code';

    // Set up intersection observer for reveal animations
    const observerOptions = {
      root: null,
      rootMargin: '0px',
      threshold: 0.1
    };

    const handleIntersect = (entries: IntersectionObserverEntry[], observer: IntersectionObserver) => {
      entries.forEach(entry => {
        if (entry.isIntersecting) {
          // Add revealed class to all reveal items within this section
          const section = entry.target;
          const revealItems = section.querySelectorAll('.reveal-item');
          revealItems.forEach((item, index) => {
            // Add staggered delay for smoother reveal
            setTimeout(() => {
              item.classList.add('revealed');
            }, index * 100);
          });

          // Once revealed, no need to observe anymore
          observer.unobserve(section);
        }
      });
    };

    const observer = new IntersectionObserver(handleIntersect, observerOptions);

    // Observe all sections with reveal-section class
    document.querySelectorAll('.reveal-section').forEach(section => {
      observer.observe(section);
    });

    return () => {
      // Clean up observer
      observer.disconnect();
    };
  }, []);

  return (
    <div className="bg-black text-white">
      <CustomCursor />
      <Header />
      <HeroSection />
      <AboutSection />
      <ServicesSection />
      <ProjectsSection />
      <ProcessSection />
      <IndustriesSection />
      <TestimonialsSection />
      <TeamSection />

      {/* Enhanced Final Call to Action */}
      <section className="final-cta relative overflow-hidden mb-0">


        {/* Animated background elements */}
        <div className="absolute top-20 -left-20 w-64 h-64 rounded-full bg-neon-green/5 blur-3xl"></div>
        <div className="absolute bottom-20 -right-20 w-72 h-72 rounded-full bg-neon-green/5 blur-3xl"></div>
        <div className="absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 w-1/2 h-1/2 rounded-full bg-neon-green/5 blur-3xl opacity-50"></div>

      </section>

      <DinoGame />
      <ContactSection />
      <Footer />
    </div>
  );
};

export default Index;
