<svg width="300" height="150" viewBox="0 0 300 150" fill="none" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <filter id="neonGlow" x="-50%" y="-50%" width="200%" height="200%">
      <feGaussianBlur stdDeviation="4" result="coloredBlur"/>
      <feMerge>
        <feMergeNode in="coloredBlur"/>
        <feMergeNode in="SourceGraphic"/>
      </feMerge>
    </filter>
  </defs>

  <!-- Dino silhouette inspired by Chrome dino game -->
  <g filter="url(#neonGlow)">
    <!-- Main body -->
    <path d="M50 80 Q50 60 70 60 L180 60 Q200 60 200 80 L200 100 Q200 120 180 120 L70 120 Q50 120 50 100 Z" fill="#00FF9D"/>

    <!-- Head -->
    <path d="M180 60 Q220 60 240 40 Q250 30 250 20 Q250 10 240 10 Q220 10 200 30 Q190 40 180 60" fill="#00FF9D"/>

    <!-- Eye -->
    <circle cx="225" cy="25" r="4" fill="#000000"/>
    <circle cx="227" cy="23" r="1.5" fill="#00FF9D"/>

    <!-- Mouth -->
    <path d="M240 35 Q245 38 240 40" stroke="#000000" stroke-width="1.5" fill="none"/>

    <!-- Back spikes -->
    <polygon points="80,60 85,45 90,60" fill="#00FF9D"/>
    <polygon points="100,60 105,45 110,60" fill="#00FF9D"/>
    <polygon points="120,60 125,45 130,60" fill="#00FF9D"/>
    <polygon points="140,60 145,45 150,60" fill="#00FF9D"/>
    <polygon points="160,60 165,45 170,60" fill="#00FF9D"/>

    <!-- Tail -->
    <path d="M50 80 Q30 75 20 70 Q10 65 15 60 Q25 65 40 75 Q50 80 50 85" fill="#00FF9D"/>

    <!-- Front arm -->
    <rect x="170" y="90" width="6" height="20" fill="#00FF9D"/>
    <ellipse cx="173" cy="115" rx="8" ry="4" fill="#00FF9D"/>

    <!-- Legs -->
    <rect x="90" y="120" width="8" height="20" fill="#00FF9D"/>
    <rect x="130" y="120" width="8" height="20" fill="#00FF9D"/>

    <!-- Feet -->
    <ellipse cx="94" cy="145" rx="12" ry="4" fill="#00FF9D"/>
    <ellipse cx="134" cy="145" rx="12" ry="4" fill="#00FF9D"/>

    <!-- Toe claws -->
    <polygon points="88,145 85,148 88,148" fill="#00FF9D"/>
    <polygon points="94,145 91,148 94,148" fill="#00FF9D"/>
    <polygon points="100,145 97,148 100,148" fill="#00FF9D"/>

    <polygon points="128,145 125,148 128,148" fill="#00FF9D"/>
    <polygon points="134,145 131,148 134,148" fill="#00FF9D"/>
    <polygon points="140,145 137,148 140,148" fill="#00FF9D"/>
  </g>
</svg>
