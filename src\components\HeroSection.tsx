
"use client"

import { motion, useInView } from "framer-motion"
import { ArrowR<PERSON>, Code, Cpu, Layers, Smartphone } from "lucide-react"
import { useRef } from "react"

function HeroSection() {
  const ref = useRef(null)
  const isInView = useInView(ref, { once: true })

  return (
    <section id="home" className="relative pt-32 pb-20 md:pt-40 md:pb-32 overflow-hidden grid-bg">
      {/* Background elements */}
      <div className="absolute inset-0 -z-10 overflow-hidden">
        <div className="absolute top-0 left-0 w-full h-full bg-grid-pattern opacity-[0.03] dark:opacity-[0.05]" />

        {/* Animated gradient blobs */}
        <motion.div
          animate={{
            x: [0, 30, 0],
            y: [0, 40, 0],
            scale: [1, 1.2, 1],
          }}
          transition={{
            duration: 20,
            repeat: Number.POSITIVE_INFINITY,
            repeatType: "reverse",
          }}
          className="absolute top-1/4 -left-20 w-72 h-72 bg-[#00FF9D] rounded-full filter blur-[120px] opacity-20 dark:opacity-10"
        />
        <motion.div
          animate={{
            x: [0, -30, 0],
            y: [0, -40, 0],
            scale: [1, 1.1, 1],
          }}
          transition={{
            duration: 15,
            repeat: Number.POSITIVE_INFINITY,
            repeatType: "reverse",
          }}
          className="absolute bottom-1/4 -right-20 w-72 h-72 bg-[#00FF9D] rounded-full filter blur-[120px] opacity-20 dark:opacity-10"
        />
      </div>

      <div className="container mx-auto px-4">
        <div className="flex flex-col items-center text-center max-w-5xl mx-auto" ref={ref}>
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={isInView ? { opacity: 1, y: 0 } : {}}
            transition={{ duration: 0.5 }}
            className="inline-flex items-center rounded-full px-4 py-1.5 text-sm font-medium bg-[#00FF9D]/10 border border-[#00FF9D]/20 mb-6"
          >
            <span className="flex h-2 w-2 rounded-full bg-[#00FF9D] mr-2 animate-pulse"></span>
            Digital Solutions Agency
          </motion.div>

          <motion.h1
            initial={{ opacity: 0, y: 20 }}
            animate={isInView ? { opacity: 1, y: 0 } : {}}
            transition={{ duration: 0.5, delay: 0.1 }}
            className="text-5xl md:text-7xl font-bold tracking-tight mb-6 leading-[1.1]"
          >
            Nerds Who Turn Your{" "}
            <span className="bg-clip-text text-transparent bg-gradient-to-r from-[#00FF9D] to-[#00FF9D]/70 relative inline-block">
              Complex Ideas
              <motion.svg
                width="100%"
                height="8"
                viewBox="0 0 100 8"
                fill="none"
                xmlns="http://www.w3.org/2000/svg"
                className="absolute -bottom-2 left-0 w-full"
                initial={{ pathLength: 0, opacity: 0 }}
                animate={isInView ? { pathLength: 1, opacity: 1 } : {}}
                transition={{ duration: 1, delay: 0.5 }}
              >
                <path
                  d="M1 5.5C20 -0.5 50 -0.5 99 5.5"
                  stroke="url(#paint0_linear)"
                  strokeWidth="2"
                  strokeLinecap="round"
                />
                <defs>
                  <linearGradient id="paint0_linear" x1="1" y1="5" x2="99" y2="5" gradientUnits="userSpaceOnUse">
                    <stop stopColor="#00FF9D" />
                    <stop offset="1" stopColor="#00FF9D" stopOpacity="0.6" />
                  </linearGradient>
                </defs>
              </motion.svg>
            </span>{" "}
            into Simple, Brilliant Solutions
          </motion.h1>

          <motion.p
            initial={{ opacity: 0, y: 20 }}
            animate={isInView ? { opacity: 1, y: 0 } : {}}
            transition={{ duration: 0.5, delay: 0.2 }}
            className="text-xl text-muted-foreground mb-8 max-w-2xl"
          >
            Think Big. Design Smart. Develop Fast. Launch Success. Repeat Forever.
          </motion.p>

          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={isInView ? { opacity: 1, y: 0 } : {}}
            transition={{ duration: 0.5, delay: 0.3 }}
            className="flex flex-col sm:flex-row gap-4"
          >
            <a href="#contact" className="neon-button group">
              <span className="relative flex items-center">
                Start Building Your Project
                <motion.div
                  className="inline-block ml-2"
                  initial={{ x: 0 }}
                  whileHover={{ x: 5 }}
                  transition={{ type: "spring", stiffness: 400, damping: 10 }}
                >
                  <ArrowRight className="h-4 w-4" />
                </motion.div>
              </span>
            </a>

            <a href="#contact" className="schedule-meeting-btn relative overflow-hidden border border-neon-green rounded-md px-6 py-3 text-white font-fira hover:scale-105 transition-transform">
              <span className="relative z-10 flex items-center">
                Schedule a Meeting
              </span>
            </a>
          </motion.div>

          {/* Floating icons */}
          <div className="relative mt-20 w-full max-w-3xl mx-auto">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={isInView ? { opacity: 1, y: 0 } : {}}
              transition={{ duration: 0.5, delay: 0.5 }}
              className="grid grid-cols-2 md:grid-cols-4 gap-6"
            >
              {[
                { icon: <Code className="h-6 w-6" />, label: "Web Development" },
                { icon: <Smartphone className="h-6 w-6" />, label: "App Development" },
                { icon: <Cpu className="h-6 w-6" />, label: "AI Solutions" },
                { icon: <Layers className="h-6 w-6" />, label: "3D Experiences" },
              ].map((item, index) => (
                <motion.div
                  key={index}
                  initial={{ y: 20, opacity: 0 }}
                  animate={isInView ? { y: 0, opacity: 1 } : {}}
                  transition={{ delay: 0.6 + index * 0.1 }}
                  whileHover={{ y: -5, transition: { duration: 0.2 } }}
                  className="flex flex-col items-center p-6 rounded-xl bg-background/50 backdrop-blur-sm border border-border/50 shadow-lg hover:shadow-xl transition-all duration-300"
                >
                  <div className="p-3 rounded-full bg-[#00FF9D]/10 mb-3 text-[#00FF9D]">{item.icon}</div>
                  <span className="text-sm font-medium text-white">{item.label}</span>
                </motion.div>
              ))}
            </motion.div>
          </div>


        </div>
      </div>

      {/* <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ delay: 1.2, duration: 0.5 }}
        className="absolute bottom-10 left-0 w-full flex justify-center animate-bounce"
      >
        <a href="#services" className="text-white opacity-75 hover:opacity-100">
          <div className="border-l-2 border-r-2 border-white h-6 w-4 rounded-full"></div>
        </a>
      </motion.div> */}
    </section>
  )
}

export default HeroSection;