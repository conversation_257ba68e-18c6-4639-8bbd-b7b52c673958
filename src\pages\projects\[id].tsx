import React, { useEffect, useState } from 'react';
import { useParams, useNavigate, Link } from 'react-router-dom';
import { ArrowLeft } from 'lucide-react';
import { featuredProjects } from '../../components/ProjectsData';
import CustomCursor from '@/components/CustomCursor';
import Header from '@/components/Header';
import Footer from '@/components/Footer';

interface Project {
  id: string;
  title: string;
  category: string;
  image: string;
  description?: string;
  technologies?: string[];
  challenge?: string;
  solution?: string;
  results?: string;
}

const ProjectDetail: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const [project, setProject] = useState<Project | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    // Scroll to the top when the component mounts
    window.scrollTo(0, 0);

    // Find the project with the matching ID
    const foundProject = featuredProjects.find(p => p.id === id) || {
      id: id || "unknown",
      title: "Demo Project",
      category: "Example Category",
      image: "https://images.unsplash.com/photo-1581291518633-83b4ebd1d83e?q=80&w=1000&auto=format&fit=crop",
      description: "This is a placeholder for a project that doesn't have detailed information yet.",
      technologies: ["React", "TypeScript", "Tailwind CSS"],
      challenge: "Example challenge description would go here.",
      solution: "Example solution description would go here.",
      results: "Example results would go here."
    };

    setProject(foundProject);
    setLoading(false);
  }, [id]);

  if (loading) {
    return (
      <div className="min-h-screen grid-bg">
        <CustomCursor />
        <Header />
        <div className="flex items-center justify-center" style={{ height: 'calc(100vh - 200px)' }}>
          <div className="text-neon-green text-xl">Loading project details...</div>
        </div>
        <Footer />
      </div>
    );
  }

  if (!project) {
    return (
      <div className="min-h-screen grid-bg">
        <CustomCursor />
        <Header />
        <div className="flex items-center justify-center" style={{ height: 'calc(100vh - 200px)' }}>
          <div className="text-center">
            <h1 className="text-2xl font-bold mb-4">Project Not Found</h1>
            <button
              onClick={() => navigate('/')}
              className="text-neon-green hover:underline flex items-center gap-2 mx-auto"
            >
              <ArrowLeft size={16} />
              Back to Home
            </button>
          </div>
        </div>
        <Footer />
      </div>
    );
  }

  return (
    <div className="min-h-screen grid-bg">
      <CustomCursor />
      <Header />
      <div className="container mx-auto px-4 py-32">
        <button
          onClick={() => navigate(-1)}
          className="text-neon-green hover:underline flex items-center gap-2 mb-8"
        >
          <ArrowLeft size={16} />
          Back to Projects
        </button>

        <div className="bg-black/40 backdrop-blur-lg border border-neon-green/10 rounded-lg overflow-hidden">
          <div className="h-[400px] relative">
            <img
              src={project.image}
              alt={project.title}
              className="w-full h-full object-cover"
            />
            <div className="absolute inset-0 bg-gradient-to-t from-black to-transparent"></div>
            <div className="absolute bottom-0 left-0 p-8 w-full">
              <div className="text-neon-green mb-2">{project.category}</div>
              <h1 className="text-4xl font-bold text-white">{project.title}</h1>
            </div>
          </div>

          <div className="p-8">
            <div className="mb-8">
              <h2 className="text-2xl font-bold mb-4 text-white">Project Overview</h2>
              <p className="text-gray-300">{project.description}</p>
            </div>

            <div className="mb-8">
              <h2 className="text-2xl font-bold mb-4 text-white">Technologies Used</h2>
              <div className="flex flex-wrap gap-2">
                {project.technologies?.map((tech, index) => (
                  <span
                    key={index}
                    className="px-3 py-1 bg-neon-green/10 border border-neon-green/20 rounded-full text-neon-green text-sm"
                  >
                    {tech}
                  </span>
                ))}
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
              <div className="bg-black/30 border border-white/5 rounded-lg p-6">
                <h3 className="text-xl font-bold mb-3 text-white">The Challenge</h3>
                <p className="text-gray-300">{project.challenge}</p>
              </div>

              <div className="bg-black/30 border border-white/5 rounded-lg p-6">
                <h3 className="text-xl font-bold mb-3 text-white">Our Solution</h3>
                <p className="text-gray-300">{project.solution}</p>
              </div>

              <div className="bg-black/30 border border-white/5 rounded-lg p-6">
                <h3 className="text-xl font-bold mb-3 text-white">The Results</h3>
                <p className="text-gray-300">{project.results}</p>
              </div>
            </div>

            <div className="text-center">
              <Link
                to="/#contact"
                className="inline-block px-6 py-3 bg-neon-green/10 border border-neon-green text-neon-green rounded-md hover:bg-neon-green/20 transition-colors"
              >
                Start a Similar Project
              </Link>
            </div>
          </div>
        </div>
      </div>
      <Footer />
    </div>
  );
};

export default ProjectDetail;
