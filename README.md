# NerdLab

NerdLab is a modern, component-driven web application built with [React](https://react.dev/), [TypeScript](https://www.typescriptlang.org/), [Vite](https://vitejs.dev/), and [Tailwind CSS](https://tailwindcss.com/). It features a modular architecture, reusable UI components, and a clean, scalable codebase suitable for rapid development and deployment.

## Table of Contents

- [Features](#features)
- [Project Structure](#project-structure)
- [Getting Started](#getting-started)
- [Available Scripts](#available-scripts)
- [Customization](#customization)
- [Contributing](#contributing)
- [License](#license)
- [Contact](#contact)

## Features

- ⚡️ Fast development with Vite
- 🛠️ Written in TypeScript for type safety
- 🎨 Styled with Tailwind CSS
- 🧩 Modular, reusable UI components
- 📱 Responsive design
- 🧪 Integrated unit testing support
- 🔥 Hot Module Replacement (HMR)
- 📝 Linting and formatting with ESLint

## Project Structure

```
nerdlab/
├── public/                 # Static assets
│   ├── favicon.ico
│   ├── placeholder.svg
│   └── robots.txt
├── src/
│   ├── components/         # Main UI components
│   │   ├── AboutSection.tsx
│   │   ├── ClientsSection.tsx
│   │   ├── ContactSection.tsx
│   │   ├── CustomCursor.tsx
│   │   ├── DinoGame.tsx
│   │   ├── Footer.tsx
│   │   ├── Header.tsx
│   │   ├── HeroSection.tsx
│   │   ├── IndustriesSection.tsx
│   │   ├── ProcessSection.tsx
│   │   ├── ProjectsData.ts
│   │   ├── ProjectsSection.tsx
│   │   ├── ServicesSection.tsx
│   │   ├── TeamSection.tsx
│   │   ├── TestimonialsSection.tsx
│   │   └── ui/             # UI primitives (buttons, cards, etc.)
│   ├── hooks/              # Custom React hooks
│   ├── lib/                # Utility functions
│   ├── pages/              # Page components
│   │   ├── Index.tsx
│   │   ├── NotFound.tsx
│   │   └── projects/
│   │       └── [id].tsx
│   ├── App.tsx             # Root component
│   ├── main.tsx            # Entry point
│   ├── App.css
│   ├── index.css
│   └── vite-env.d.ts
├── .gitignore
├── package.json
├── tsconfig.json
├── vite.config.ts
├── tailwind.config.ts
├── postcss.config.js
├── eslint.config.js
├── README.md
└── ... (other config files)
```

## Getting Started

### Prerequisites

- [Node.js](https://nodejs.org/) (v16+ recommended)
- [npm](https://www.npmjs.com/) or [bun](https://bun.sh/)

### Installation

Clone the repository:

```bash
git clone https://github.com/your-username/nerdlab-future-forge.git
cd nerdlab-future-forge
```

Install dependencies:

```bash
npm install
# or
bun install
```

### Running the Development Server

```bash
npm run dev
# or
bun run dev
```

The app will be available at [http://localhost:5173](http://localhost:5173) by default.

### Building for Production

```bash
npm run build
# or
bun run build
```

### Preview Production Build

```bash
npm run preview
# or
bun run preview
```

## Available Scripts

- `dev` – Start the development server
- `build` – Build the app for production
- `preview` – Preview the production build
- `lint` – Run ESLint for code quality

## Customization

- **Tailwind CSS**: Edit `tailwind.config.ts` to customize the design system.
- **Components**: Add or modify components in `src/components/`.
- **Pages**: Add new pages in `src/pages/`.
- **Configuration**: Update `vite.config.ts`, `tsconfig.json`, and other config files as needed.
