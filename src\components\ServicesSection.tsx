"use client"

import React, { useRef } from "react";
import { motion, useScroll, useTransform } from "framer-motion";
import {
  Palette,
  BrainCircuit,
  Code2,
  Smartphone,
  Box,
  Zap,
  LayoutDashboard,
  Glasses,
  ShoppingBag
} from 'lucide-react';

interface ServiceCardProps {
  title: string;
  icon: React.ReactNode;
  description: string;
  index: number;
  iconBg: string;
}

const getServiceDetails = (title: string): string[] => {
  switch (title) {
    case "Web Development":
      return [
        "Server-side rendering with Next.js",
        "Static site generation with Astro",
        "Progressive Web Apps (PWA)",
        "API integration and development"
      ];
    case "App Development":
      return [
        "Native iOS and Android development",
        "Cross-platform with React Native",
        "Real-time data synchronization",
        "Offline-first capabilities"
      ];
    case "UI/UX Design":
      return [
        "User research and personas",
        "Wireframing and prototyping",
        "Design system development",
        "Usability testing"
      ];
    case "E-Commerce & SaaS":
      return [
        "Custom shopping experiences",
        "Payment gateway integration",
        "Subscription management",
        "Analytics and reporting"
      ];
    case "3D Web Experiences":
      return [
        "Interactive 3D models",
        "WebGL animations",
        "3D product configurators",
        "Virtual showrooms"
      ];
    case "AI-Powered Applications":
      return [
        "Natural language processing",
        "Computer vision integration",
        "Predictive analytics",
        "ChatGPT integration"
      ];
    case "Automation Tools":
      return [
        "Custom workflow automation",
        "Task scheduling systems",
        "Data processing pipelines",
        "Integration with existing tools"
      ];
    case "Dashboards & Admin":
      return [
        "Real-time data visualization",
        "Custom reporting tools",
        "Role-based access control",
        "System monitoring"
      ];
    case "VR/AR Integrations":
      return [
        "WebXR applications",
        "AR product visualization",
        "Virtual training solutions",
        "360° experiences"
      ];
    default:
      return [];
  }
};

const ServiceCard: React.FC<ServiceCardProps> = ({ title, icon, description, index, iconBg }) => {
  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      whileInView={{ opacity: 1, y: 0 }}
      viewport={{ once: true, margin: "-50px" }}
      transition={{ duration: 0.5, delay: index * 0.1 }}
      whileHover={{
        y: -10,
        transition: { duration: 0.2 }
      }}
      className="relative overflow-hidden rounded-xl bg-black/40 backdrop-blur-sm border border-neon-green/10 p-8 group min-h-[300px]"
    >
      {/* Background gradient */}
      <div className="absolute inset-0 bg-gradient-to-br from-black via-black to-transparent opacity-90 z-0"></div>

      {/* Enlarged Icon Background */}
      <div className="absolute inset-0 z-0">
        {React.cloneElement(icon as React.ReactElement, { 
          size: 200,
          className: "absolute right-0 bottom-0 transform translate-x-1/4 translate-y-1/4 opacity-[0.03] transition-all duration-300 group-hover:opacity-[0.06]" 
        })}
      </div>

      {/* Content */}
      <div className="relative z-10">
        <div className="flex items-center mb-4">
          <div className="w-16 h-16 rounded-xl bg-neon-green/10 border border-neon-green/20 flex items-center justify-center mr-4 transition-all duration-300 group-hover:bg-neon-green/20">
            <motion.div
              className="text-neon-green"
              whileHover={{ rotate: 360 }}
              transition={{ duration: 0.5 }}
            >
              {React.cloneElement(icon as React.ReactElement, { size: 32 })}
            </motion.div>
          </div>
          <h3 className="text-2xl font-medium">{title}</h3>
        </div>
        <div className="mt-4 space-y-4">
          <p className="text-gray-300 text-base leading-relaxed">{description}</p>
          <ul className="text-gray-400 text-sm space-y-2 list-disc list-inside">
            {getServiceDetails(title).map((detail, i) => (
              <li key={i}>{detail}</li>
            ))}
          </ul>
        </div>
      </div>
    </motion.div>
  );
};

const ServicesSection: React.FC = () => {
  const sectionRef = useRef(null);
  const { scrollYProgress } = useScroll({
    target: sectionRef,
    offset: ["start end", "end start"],
  });

  const y1 = useTransform(scrollYProgress, [0, 1], [50, -50]);
  const y2 = useTransform(scrollYProgress, [0, 1], [-50, 50]);
  const opacity = useTransform(scrollYProgress, [0, 0.2, 0.8, 1], [0, 1, 1, 0]);

  const services = [
    {
      title: "Web Development",
      icon: <Code2 size={28} />,
      description: "Modern, high-performance web applications with cutting-edge frameworks and optimized user experiences.",
      iconBg: "radial-gradient(circle, rgba(0,255,157,0.3) 0%, transparent 70%)"
    },
    {
      title: "App Development",
      icon: <Smartphone size={28} />,
      description: "Native-like mobile experiences for iOS and Android with seamless cross-platform functionality.",
      iconBg: "radial-gradient(circle, rgba(0,255,157,0.3) 0%, transparent 70%)"
    },
    {
      title: "UI/UX Design",
      icon: <Palette size={28} />,
      description: "Human-centered design solutions that create memorable and intuitive digital experiences.",
      iconBg: "radial-gradient(circle, rgba(0,255,157,0.3) 0%, transparent 70%)"
    },
    {
      title: "E-Commerce & SaaS",
      icon: <ShoppingBag size={28} />,
      description: "Full-featured digital commerce solutions and subscription-based platforms optimized for growth.",
      iconBg: "radial-gradient(circle, rgba(0,255,157,0.3) 0%, transparent 70%)"
    },
    {
      title: "3D Web Experiences",
      icon: <Box size={28} />,
      description: "Interactive and immersive 3D web experiences that push the boundaries of digital interaction.",
      iconBg: "radial-gradient(circle, rgba(0,255,157,0.3) 0%, transparent 70%)"
    },
    {
      title: "AI-Powered Applications",
      icon: <BrainCircuit size={28} />,
      description: "Smart solutions powered by advanced AI algorithms and machine learning technologies.",
      iconBg: "radial-gradient(circle, rgba(0,255,157,0.3) 0%, transparent 70%)"
    },
    {
      title: "Automation Tools",
      icon: <Zap size={28} />,
      description: "Custom automation tools and workflows that streamline business processes and boost efficiency.",
      iconBg: "radial-gradient(circle, rgba(0,255,157,0.3) 0%, transparent 70%)"
    },
    {
      title: "Dashboards & Admin",
      icon: <LayoutDashboard size={28} />,
      description: "Intuitive and powerful administrative interfaces for data visualization and system management.",
      iconBg: "radial-gradient(circle, rgba(0,255,157,0.3) 0%, transparent 70%)"
    },
    {
      title: "VR/AR Integrations",
      icon: <Glasses size={28} />,
      description: "Cutting-edge virtual and augmented reality experiences for innovative applications.",
      iconBg: "radial-gradient(circle, rgba(0,255,157,0.3) 0%, transparent 70%)"
    }
  ];

  return (
    <section id="services" className="py-24 grid-bg relative overflow-hidden" ref={sectionRef}>
      {/* Background elements */}
      <div className="absolute inset-0 -z-10">
        <motion.div
          style={{ y: y1, opacity }}
          className="absolute top-0 right-1/4 w-72 h-72 bg-neon-green/5 rounded-full filter blur-[80px]"
        />
        <motion.div
          style={{ y: y2, opacity }}
          className="absolute bottom-0 left-1/4 w-72 h-72 bg-neon-green/5 rounded-full filter blur-[80px]"
        />
      </div>

      {/* Large background icons */}
      <div className="absolute inset-0 overflow-hidden -z-5">
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 0.03 }}
          transition={{ duration: 1 }}
          className="absolute -top-20 -left-20 text-neon-green/10"
        >
          <Palette size={300} />
        </motion.div>
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 0.03 }}
          transition={{ duration: 1, delay: 0.2 }}
          className="absolute top-1/4 right-0 text-neon-green/10"
        >
          <Code2 size={250} />
        </motion.div>
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 0.03 }}
          transition={{ duration: 1, delay: 0.4 }}
          className="absolute bottom-0 left-1/3 text-neon-green/10"
        >
          <BrainCircuit size={280} />
        </motion.div>
      </div>

      <div className="container mx-auto px-4 relative z-10">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true, margin: "-100px" }}
          transition={{ duration: 0.5 }}
          className="text-center max-w-3xl mx-auto mb-16"
        >
          <h2 className="text-3xl md:text-4xl font-bold mb-6">
            Our{" "}
            <span className="bg-clip-text text-transparent bg-gradient-to-r from-neon-green to-neon-green/70 relative">
              Services
              <motion.div
                className="absolute -bottom-1 left-0 h-[3px] bg-gradient-to-r from-neon-green to-neon-green/70 rounded-full"
                initial={{ width: 0 }}
                whileInView={{ width: "100%" }}
                transition={{ duration: 0.5, delay: 0.2 }}
              />
            </span>
          </h2>
          <p className="text-gray-400 text-lg">
            We deliver cutting-edge digital solutions tailored to your unique business needs
          </p>
        </motion.div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          {services.map((service, index) => (
            <ServiceCard
              key={index}
              title={service.title}
              icon={service.icon}
              description={service.description}
              index={index}
              iconBg={service.iconBg}
            />
          ))}
        </div>
      </div>
    </section>
  );
};

export default ServicesSection;