
#root {
  margin: 0 auto;
  text-align: center;
}



/* Enhanced Final CTA styles */
.final-cta {
  position: relative;
  overflow: hidden;
  padding: 100px 0;
  margin: 80px 0 0;
  background: linear-gradient(to bottom, rgba(0,0,0,0.8), rgba(0,0,0,1));
}

.final-cta::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background:
    radial-gradient(circle at 20% 30%, rgba(0,255,157,0.15) 0%, transparent 50%),
    radial-gradient(circle at 80% 70%, rgba(0,255,157,0.15) 0%, transparent 50%);
  z-index: 0;
}

.cta-button {
  position: relative;
  overflow: hidden;
  margin-top: 2rem;
  padding: 1.5rem 4rem;
  font-size: 1.5rem;
  font-weight: 500;
  background: rgba(0, 0, 0, 0.6);
  border: 2px solid rgba(0, 255, 157, 0.7);
  border-radius: 8px;
  transition: all 0.3s;
  box-shadow: 0 0 30px rgba(0, 255, 157, 0.3);
  z-index: 1;
}

.cta-button::before {
  content: '';
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: conic-gradient(transparent, rgba(0, 255, 157, 0.3), transparent 30%);
  animation: rotate 4s linear infinite;
  z-index: -2;
}

.cta-button::after {
  content: '';
  position: absolute;
  inset: 4px;
  background: rgba(0, 0, 0, 0.8);
  border-radius: 4px;
  transition: background-color 0.3s ease;
  z-index: -1;
}

.cta-button:hover {
  transform: scale(1.05);
  box-shadow: 0 0 40px rgba(0, 255, 157, 0.5);
}

.cta-button:hover::after {
  background: rgba(0, 255, 157, 0.2);
}

@keyframes rotate {
  100% {
    transform: rotate(1turn);
  }
}

/* Hide scrollbar for horizontal scrolling */
.hide-scrollbar {
  -ms-overflow-style: none;  /* IE and Edge */
  scrollbar-width: none;  /* Firefox */
}

.hide-scrollbar::-webkit-scrollbar {
  display: none; /* Chrome, Safari, Opera*/
}

/* Custom styles for the team section */
.team-card-container {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(220px, 1fr));
  gap: 1rem;
}

.team-card {
  aspect-ratio: 1/1;
  position: relative;
  border-radius: 0.5rem;
  overflow: hidden;
}

/* Enhanced WhatsApp button styling */
.schedule-meeting-btn {
  position: relative;
  overflow: hidden;
  z-index: 1;
}

.schedule-meeting-btn::before {
  content: '';
  position: absolute;
  inset: 0;
  background: linear-gradient(135deg, rgba(0,255,157,0.2), rgba(0,0,0,0) 50%);
  z-index: -1;
  transform: translateX(-100%);
  transition: transform 0.6s cubic-bezier(0.23, 1, 0.32, 1);
}

.schedule-meeting-btn:hover::before {
  transform: translateX(0);
}

/* Projects grid styling */
.projects-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
  gap: 1.5rem;
}

/* Project card enhancements */
.project-card {
  overflow: hidden;
  position: relative;
  border-radius: 8px;
  transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
}

.project-card:hover {
  transform: translateY(-10px);
  box-shadow: 0 15px 30px rgba(0,0,0,0.5), 0 0 30px rgba(0,255,157,0.3);
}

.project-card::after {
  content: '';
  position: absolute;
  inset: 0;
  background: linear-gradient(to bottom, transparent 70%, rgba(0,0,0,0.8));
  opacity: 0;
  transition: opacity 0.3s;
}

.project-card:hover::after {
  opacity: 1;
}

/* Fix for mobile */
@media (max-width: 640px) {
  * {
    cursor: auto;
  }

  .custom-cursor {
    display: none;
  }


}
