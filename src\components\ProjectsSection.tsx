import React from 'react';
import { Link } from 'react-router-dom';
import { motion } from 'framer-motion';
import { Carousel, CarouselContent, CarouselItem } from "@/components/ui/carousel";
import { featuredProjects } from './ProjectsData';
import { Badge } from "@/components/ui/badge";

const ProjectCard: React.FC<{ project: typeof featuredProjects[0] }> = ({ project }) => {
  return (
    <Link
      to={`/projects/${project.id}`}
      className="block project-card bg-black/40 backdrop-blur-lg border border-neon-green/10 rounded-xl overflow-hidden transition-all duration-500 group hover:border-neon-green/30"
    >
      <div className="relative">
        <img
          src={project.image}
          alt={project.title}
          className="w-full h-[300px] object-cover transition-transform duration-500 group-hover:scale-105"
          loading="lazy"
        />
        <div className="absolute inset-0 bg-gradient-to-t from-black via-black/80 to-transparent"></div>
        <div className="absolute bottom-0 left-0 p-6 w-full">
          <div className="space-y-4">
            <div>
              <div className="inline-block bg-neon-green/20 border border-neon-green/30 rounded-full px-3 py-1 text-neon-green text-xs mb-2">
                {project.category}
              </div>
              <h3 className="text-white text-xl font-bold mb-2">{project.title}</h3>
              <p className="text-gray-300 text-sm line-clamp-2 mb-3">
                {project.description}
              </p>
            </div>
            
            <div className="flex flex-wrap gap-2 mb-3">
              {project.technologies.slice(0, 4).map((tech, index) => (
                <Badge 
                  key={index}
                  variant="outline" 
                  className="bg-black/50 text-gray-300 border-gray-700 text-xs"
                >
                  {tech}
                </Badge>
              ))}
              {project.technologies.length > 4 && (
                <Badge 
                  variant="outline" 
                  className="bg-black/50 text-gray-300 border-gray-700 text-xs"
                >
                  +{project.technologies.length - 4} more
                </Badge>
              )}
            </div>

            <div className="flex items-center text-neon-green text-sm transition-opacity duration-300">
              <span>View Project Details</span>
              <svg 
                className="w-4 h-4 ml-1 transform transition-transform duration-300 group-hover:translate-x-1" 
                fill="none" 
                viewBox="0 0 24 24" 
                stroke="currentColor"
              >
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
              </svg>
            </div>
          </div>
        </div>
      </div>
    </Link>
  );
};

const ProjectsSection: React.FC = () => {
  return (
    <section id="projects" className="py-24 grid-bg relative overflow-hidden">
      <div className="container mx-auto px-4">
        <div className="text-center mb-12">
          <h2 className="text-3xl md:text-4xl font-bold mb-4">
            Our{" "}
            <span className="bg-clip-text text-transparent bg-gradient-to-r from-neon-green to-neon-green/70 relative">
              Projects
              <motion.div
                className="absolute -bottom-1 left-0 h-[3px] bg-gradient-to-r from-neon-green to-neon-green/70 rounded-full"
                initial={{ width: 0 }}
                whileInView={{ width: "100%" }}
                transition={{ duration: 0.5, delay: 0.2 }}
              />
            </span>
          </h2>
          <p className="text-gray-400 max-w-2xl mx-auto">
            Explore our portfolio of innovative solutions across different industries and technologies.
          </p>
        </div>

        <div className="mb-8">
          <div className="relative">
            <div className="absolute left-0 top-0 w-32 h-full bg-gradient-to-r from-black via-black/80 to-transparent z-10"></div>
            <div className="absolute right-0 top-0 w-32 h-full bg-gradient-to-l from-black via-black/80 to-transparent z-10"></div>

            <Carousel className="w-full" opts={{ loop: true, align: "start" }}>
              <CarouselContent className="animate-infinite-scroll">
                {featuredProjects.slice(0, 4).map((project, index) => (
                  <CarouselItem key={`${project.id}-${index}`} className="md:basis-1/2 lg:basis-1/2 p-2">
                    <ProjectCard project={project} />
                  </CarouselItem>
                ))}
              </CarouselContent>
            </Carousel>
          </div>

          <div className="relative">
            <div className="absolute left-0 top-0 w-32 h-full bg-gradient-to-r from-black via-black/80 to-transparent z-10"></div>
            <div className="absolute right-0 top-0 w-32 h-full bg-gradient-to-l from-black via-black/80 to-transparent z-10"></div>

            <Carousel className="w-full" opts={{ loop: true, align: "start", direction: "rtl" }}>
              <CarouselContent className="animate-infinite-scroll-reverse">
                {featuredProjects.slice(4, 8).map((project, index) => (
                  <CarouselItem key={`${project.id}-${index + 4}`} className="md:basis-1/2 lg:basis-1/2 p-2">
                    <ProjectCard project={project} />
                  </CarouselItem>
                ))}
              </CarouselContent>
            </Carousel>
          </div>

          <div className="relative">
            <div className="absolute left-0 top-0 w-32 h-full bg-gradient-to-r from-black via-black/80 to-transparent z-10"></div>
            <div className="absolute right-0 top-0 w-32 h-full bg-gradient-to-l from-black via-black/80 to-transparent z-10"></div>

            <Carousel className="w-full" opts={{ loop: true, align: "start" }}>
              <CarouselContent className="animate-infinite-scroll">
                {featuredProjects.slice(8, 12).map((project, index) => (
                  <CarouselItem key={`${project.id}-${index + 8}`} className="md:basis-1/2 lg:basis-1/2 p-2">
                    <ProjectCard project={project} />
                  </CarouselItem>
                ))}
              </CarouselContent>
            </Carousel>
          </div>
        </div>
      </div>
    </section>
  );
};

export default ProjectsSection;