
"use client"

import { motion, useScroll, useTransform } from "framer-motion"
import {
  Bot,
  Glasses,
  BookOpen,
  DollarSign,
  HeartPulse,
  Leaf,
  Home,
  Building2,
  BarChart,
  ShoppingCart,
  TestTube,
  Users
} from "lucide-react"
import { useRef } from "react"

function IndustriesSection() {
  const sectionRef = useRef(null)
  const { scrollYProgress } = useScroll({
    target: sectionRef,
    offset: ["start end", "end start"],
  })

  const y = useTransform(scrollYProgress, [0, 1], [50, -50])
  const opacity = useTransform(scrollYProgress, [0, 0.2, 0.8, 1], [0, 1, 1, 0])

  const industries = [
    {
      icon: <Bot size={72} />,
      name: "AI & Machine Learning",
      description: "Cutting-edge AI solutions for automation, data analysis, and intelligent decision-making systems",
      features: ["NLP", "Vision", "AI", "Learn"]
    },
    {
      icon: <Glasses size={72} />,
      name: "AR/VR and Spatial Tech",
      description: "Immersive experiences that blend digital and physical worlds for training, entertainment, and visualization",
      features: ["3D", "VR/AR", "Space", "Mix"]
    },
    {
      icon: <BookOpen size={72} />,
      name: "EdTech",
      description: "Digital learning platforms that transform education through personalized, interactive experiences",
      features: ["Learn", "VR", "Track", "UI/UX"]
    },
    {
      icon: <DollarSign size={72} />,
      name: "FinTech",
      description: "Modern financial solutions for payments, investments, and banking services",
      features: ["Bank", "Pay", "Chain", "Risk"]
    },
    {
      icon: <HeartPulse size={72} />,
      name: "HealthTech",
      description: "Digital healthcare solutions for improved patient care and medical service delivery",
      features: ["Care", "EMR", "Labs", "Doc"]
    },
    {
      icon: <Leaf size={72} />,
      name: "AgriTech",
      description: "Smart farming solutions that optimize agriculture through technology and data",
      features: ["Farm", "IoT", "Yield", "Supply"]
    },
    {
      icon: <Home size={72} />,
      name: "PropTech",
      description: "Digital solutions for real estate management, sales, and smart buildings",
      features: ["Tour", "Prop", "Smart", "Data"]
    },
    {
      icon: <Building2 size={72} />,
      name: "GovTech",
      description: "Digital transformation solutions for government services and civic engagement",
      features: ["Gov", "Sec", "Portal", "City"]
    },
    {
      icon: <BarChart size={72} />,
      name: "MarTech",
      description: "Advanced marketing tools for customer engagement and campaign optimization",
      features: ["Data", "Auto", "CRM", "Sales"]
    },
    {
      icon: <ShoppingCart size={72} />,
      name: "RetailTech",
      description: "Digital commerce solutions for modern retail experiences and operations",
      features: ["Shop", "Stock", "POS", "CRM"]
    },
    {
      icon: <TestTube size={72} />,
      name: "BioTech",
      description: "Technology solutions for biological research and medical innovations",
      features: ["Lab", "Data", "R&D", "Tests"]
    },
    {
      icon: <Users size={72} />,
      name: "HRTech",
      description: "Digital solutions for modern workforce management and employee experience",
      features: ["HR", "Perf", "Train", "Data"]
    }
  ]

  return (
    <section id="industries" className="py-24 grid-bg relative overflow-hidden" ref={sectionRef}>
      {/* Background elements */}
      <div className="absolute inset-0 -z-10">
        <motion.div
          style={{ y, opacity }}
          className="absolute top-0 right-1/3 w-64 h-64 bg-neon-green/3 rounded-full filter blur-[80px]"
        />
        <motion.div
          style={{ y: useTransform(scrollYProgress, [0, 1], [-50, 50]), opacity }}
          className="absolute bottom-0 left-1/3 w-64 h-64 bg-neon-green/3 rounded-full filter blur-[80px]"
        />
      </div>

      <div className="container mx-auto px-4">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true, margin: "-100px" }}
          transition={{ duration: 0.5 }}
          className="text-center max-w-3xl mx-auto mb-16"
        >
          <h2 className="text-3xl md:text-4xl font-bold mb-6">
            Industries We{" "}
            <span className="bg-clip-text text-transparent bg-gradient-to-r from-neon-green to-neon-green/70 relative">
              Serve
              <motion.div
                className="absolute -bottom-1 left-0 h-[3px] bg-gradient-to-r from-neon-green to-neon-green/70 rounded-full"
                initial={{ width: 0 }}
                whileInView={{ width: "100%" }}
                transition={{ duration: 0.5, delay: 0.2 }}
              />
            </span>
          </h2>
          <p className="text-gray-400 text-lg">
            We work across a diverse range of industries, bringing our technical expertise to various sectors.
          </p>
        </motion.div>

        <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6">
          {industries.map((industry, index) => (
            <motion.div
              key={index}
              initial={{ opacity: 0, scale: 0.9 }}
              whileInView={{ opacity: 1, scale: 1 }}
              viewport={{ once: true, margin: "-100px" }}
              transition={{ duration: 0.3, delay: index * 0.05 }}
              whileHover={{
                scale: 1.05,
                rotateY: 10,
                transition: { duration: 0.2 }
              }}
              className="flex flex-col items-center text-center group perspective"
            >
              <div className="w-full rounded-2xl bg-black/50 shadow-lg flex flex-col items-center p-6 border border-neon-green/10 group-hover:border-neon-green/20 group-hover:bg-neon-green/3 transition-all duration-300 relative overflow-hidden">
                {/* Glow effect */}
                <div className="absolute inset-0 bg-neon-green/0 group-hover:bg-neon-green/3 blur-xl transition-all duration-500"></div>

                {/* Icon container with animations */}
                <motion.div
                  className="text-neon-green/80 relative z-10 mb-4"
                  whileHover={{ scale: 1.1 }}
                  transition={{ type: "spring", stiffness: 400, damping: 10 }}
                >
                  {industry.icon}
                </motion.div>

                {/* Industry name */}
                <h3 className="text-lg font-bold mb-2 group-hover:text-neon-green/80 transition-colors duration-300">
                  {industry.name}
                </h3>

                {/* Description */}
                <p className="text-sm text-gray-400 mb-4 text-center">
                  {industry.description}
                </p>

                {/* Features */}
                <div className="flex flex-wrap gap-1 justify-center">
                  {industry.features.map((feature, idx) => (
                    <span
                      key={idx}
                      className="inline-block text-[12px] px-2 py-0.5 bg-black/30 rounded-sm text-neon-green/60 border border-neon-green/10 whitespace-nowrap truncate max-w-[65px] uppercase tracking-wide"
                    >
                      {feature}
                    </span>
                  ))}
                </div>

                {/* Border gradient animation */}
                <div className="absolute inset-0 bg-gradient-to-r from-neon-green/0 via-neon-green/3 to-neon-green/0 opacity-0 group-hover:opacity-70 transition-opacity duration-300"></div>
              </div>
            </motion.div>
          ))}
        </div>
      </div>
    </section>
  )
}

export default IndustriesSection;
