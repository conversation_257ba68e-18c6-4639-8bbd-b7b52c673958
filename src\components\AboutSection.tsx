"use client"

import { motion, useInView } from "framer-motion"
import { <PERSON>bul<PERSON>, Rocket } from "lucide-react"
import { useRef } from "react"

function AboutSection() {
  const ref = useRef(null)
  const isInView = useInView(ref, { once: true })

  return (
    <section id="about" className="relative py-24 overflow-hidden grid-bg">
      {/* Background elements */}
      <div className="absolute inset-0 -z-10 overflow-hidden">
        <div className="absolute top-0 left-0 w-full h-full bg-grid-pattern opacity-[0.03] dark:opacity-[0.05]" />
        <div className="absolute top-20 -left-20 w-64 h-64 rounded-full bg-neon-green/5 blur-3xl"></div>
        <div className="absolute bottom-20 -right-20 w-72 h-72 rounded-full bg-neon-green/5 blur-3xl"></div>
      </div>

      <div className="container mx-auto px-4">
        <div className="flex flex-col items-center max-w-5xl mx-auto" ref={ref}>
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={isInView ? { opacity: 1, y: 0 } : {}}
            transition={{ duration: 0.5 }}
            className="inline-flex items-center rounded-full px-4 py-1.5 text-sm font-medium bg-[#00FF9D]/10 border border-[#00FF9D]/20 mb-6"
          >
            <span className="flex h-2 w-2 rounded-full bg-[#00FF9D] mr-2 animate-pulse"></span>
            Who We Are
          </motion.div>

          <motion.h2
            initial={{ opacity: 0, y: 20 }}
            animate={isInView ? { opacity: 1, y: 0 } : {}}
            transition={{ duration: 0.5, delay: 0.1 }}
            className="text-3xl md:text-4xl font-bold mb-6 text-center"
          >
            Transforming Ideas into{" "}
            <span className="bg-clip-text text-transparent bg-gradient-to-r from-[#00FF9D] to-[#00FF9D]/70 relative inline-block">
              Digital Excellence
              <motion.svg
                width="100%"
                height="8"
                viewBox="0 0 100 8"
                fill="none"
                xmlns="http://www.w3.org/2000/svg"
                className="absolute -bottom-2 left-0 w-full"
                initial={{ pathLength: 0, opacity: 0 }}
                animate={isInView ? { pathLength: 1, opacity: 1 } : {}}
                transition={{ duration: 1, delay: 0.5 }}
              >
                <path
                  d="M1 5.5C20 -0.5 50 -0.5 99 5.5"
                  stroke="url(#paint0_linear)"
                  strokeWidth="2"
                  strokeLinecap="round"
                />
                <defs>
                  <linearGradient id="paint0_linear" x1="1" y1="5" x2="99" y2="5" gradientUnits="userSpaceOnUse">
                    <stop stopColor="#00FF9D" />
                    <stop offset="1" stopColor="#00FF9D" stopOpacity="0.6" />
                  </linearGradient>
                </defs>
              </motion.svg>
            </span>
          </motion.h2>

          <motion.p
            initial={{ opacity: 0, y: 20 }}
            animate={isInView ? { opacity: 1, y: 0 } : {}}
            transition={{ duration: 0.5, delay: 0.2 }}
            className="text-lg text-muted-foreground mb-10 text-center max-w-3xl"
          >
            Nerdlab is a cutting-edge digital solutions agency that combines technical expertise with creative innovation.
            We specialize in transforming complex ideas into elegant, functional digital products that drive business growth
            and enhance user experiences.
          </motion.p>

          {/* Lego Section */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={isInView ? { opacity: 1, y: 0 } : {}}
            transition={{ duration: 0.5, delay: 0.4 }}
            className="mt-12 w-full"
          >
            <div className="text-center mb-8">
              <h3 className="text-2xl md:text-3xl font-bold mb-4">
                Do You Like <span className="text-neon-green">Legos</span>? We Do Too!
              </h3>
              <p className="text-gray-300 max-w-2xl mx-auto">
                At Nerdlab, we believe in building things block by block, just like Legos.
                It's not just a metaphor for our development process—we're genuinely passionate about creative construction.
              </p>
            </div>

            {/* Lego Game Iframe */}
            <div className="w-full aspect-video max-w-4xl mx-auto bg-black/30 rounded-xl border border-neon-green/20 overflow-hidden relative">
              <iframe
                src="https://lego-blocks-game.vercel.app/"
                className="w-full h-full border-0"
                title="Lego Blocks Game"
                allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
                loading="lazy"
              ></iframe>
            </div>
          </motion.div>

          {/* Nerdlab Offerings */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={isInView ? { opacity: 1, y: 0 } : {}}
            transition={{ duration: 0.5, delay: 0.5 }}
            className="mt-16 w-full"
          >
            <div className="bg-black/40 backdrop-blur-sm p-8 rounded-xl border border-neon-green/20 shadow-lg">
              <p className="text-gray-300 leading-relaxed">
                Nerdlab offers tailored digital solutions with features like research-driven design, brand-aligned, scalable and efficient development, smooth performance, and complete post-launch support. We emphasize on excellence, ensuring expert craftsmanship, agile processes, and delivering speed without compromising quality, making us a comprehensive choice for innovative and high-performance projects.
              </p>
            </div>
          </motion.div>

          {/* Mission and Vision Section */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={isInView ? { opacity: 1, y: 0 } : {}}
            transition={{ duration: 0.5, delay: 0.6 }}
            className="grid grid-cols-1 md:grid-cols-2 gap-8 mt-12"
          >
            <div className="bg-black/40 backdrop-blur-sm p-6 rounded-xl border border-neon-green/20 shadow-lg">
              <h3 className="text-xl font-bold mb-4 flex items-center">
                <span className="p-2 rounded-full bg-[#00FF9D]/10 mr-3 text-[#00FF9D]">
                  <Rocket className="h-5 w-5" />
                </span>
                Our Mission
              </h3>
              <p className="text-gray-300">
                To empower businesses with innovative digital solutions that solve complex problems,
                enhance user experiences, and drive sustainable growth in an ever-evolving technological landscape.
              </p>
            </div>

            <div className="bg-black/40 backdrop-blur-sm p-6 rounded-xl border border-neon-green/20 shadow-lg">
              <h3 className="text-xl font-bold mb-4 flex items-center">
                <span className="p-2 rounded-full bg-[#00FF9D]/10 mr-3 text-[#00FF9D]">
                  <Lightbulb className="h-5 w-5" />
                </span>
                Our Vision
              </h3>
              <p className="text-gray-300">
                To be at the forefront of digital innovation, creating transformative solutions that
                shape the future of technology and set new standards for excellence in the digital realm.
              </p>
            </div>
          </motion.div>
        </div>
      </div>
    </section>
  )
}

export default AboutSection;