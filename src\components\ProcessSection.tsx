
"use client"

import React from 'react';
import { motion } from 'framer-motion';
import {
  Lightbulb,
  Search,
  Layers,
  Palette,
  Code,
  Server,
  Link,
  Bug,
  Rocket
} from 'lucide-react';

interface ProcessStepProps {
  number: number;
  title: string;
  description: string;
  icon: React.ReactNode;
  position: 'left' | 'right' | 'center';
  isLast?: boolean;
}

const ProcessStep: React.FC<ProcessStepProps> = ({ number, title, description, icon, position, isLast = false }) => {
  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      whileInView={{ opacity: 1, y: 0 }}
      viewport={{ once: true, margin: "-50px" }}
      transition={{ duration: 0.5, delay: number * 0.1 }}
      className={`relative flex items-center ${
        isLast ? 'mb-0' : 'mb-16'
      } ${
        position === 'left'
          ? 'justify-start'
          : position === 'right'
            ? 'justify-end'
            : 'justify-center'
      }`}
    >
      {/* Step Node with Icon */}
      <motion.div
        whileHover={{ scale: 1.05 }}
        transition={{ type: "spring", stiffness: 300, damping: 10 }}
        className="relative flex-shrink-0 w-20 h-20 md:w-24 md:h-24 rounded-2xl bg-black/50 border border-neon-green/30 flex items-center justify-center shadow-lg group z-10"
      >
        {/* Glow effect */}
        <div className="absolute inset-0 bg-neon-green/5 rounded-2xl blur-sm group-hover:bg-neon-green/10 transition-all duration-300"></div>

        {/* Animated icon */}
        <motion.div
          className="text-neon-green text-[2.5rem] md:text-[3.5rem] scale-[1.5]"
          whileHover={{
            rotate: [0, -5, 5, -5, 0],
            transition: { duration: 0.5 }
          }}
        >
          {icon}
        </motion.div>

        {/* Step number */}
        <motion.div
          className="absolute -top-2 -right-2 bg-neon-green text-black w-6 h-6 rounded-full flex items-center justify-center font-bold text-xs shadow-[0_0_15px_rgba(0,255,157,0.5)]"
          whileHover={{ scale: 1.2 }}
          transition={{ type: "spring", stiffness: 400, damping: 10 }}
        >
          {number}
        </motion.div>
      </motion.div>

      {/* Content Card */}
      <motion.div
        initial={{ opacity: 0, x: position === 'left' ? 20 : position === 'right' ? -20 : 0, y: position === 'center' ? 20 : 0 }}
        whileInView={{ opacity: 1, x: 0, y: 0 }}
        viewport={{ once: true }}
        transition={{ duration: 0.5, delay: number * 0.1 + 0.2 }}
        className={`absolute ${
          position === 'left'
            ? 'left-28 md:left-32'
            : position === 'right'
              ? 'right-28 md:right-32'
              : 'top-28 md:top-32'
        } bg-black/40 backdrop-blur-sm p-4 md:p-6 rounded-xl border border-neon-green/20 shadow-lg max-w-xs md:max-w-sm z-10`}
      >
        {/* Title with subtle animation */}
        <motion.h3
          className="text-xl md:text-2xl font-bold mb-2"
          initial={{ opacity: 0.8 }}
          whileInView={{ opacity: 1 }}
          viewport={{ once: true }}
          transition={{ duration: 0.3, delay: number * 0.1 + 0.3 }}
        >
          {title}
        </motion.h3>

        {/* Description with subtle animation */}
        <motion.p
          className="text-gray-300 text-sm md:text-base leading-relaxed"
          initial={{ opacity: 0.6 }}
          whileInView={{ opacity: 1 }}
          viewport={{ once: true }}
          transition={{ duration: 0.3, delay: number * 0.1 + 0.4 }}
        >
          {description}
        </motion.p>
      </motion.div>

      {/* Connector Arrow - Only show if not the last item */}
      {!isLast && (
        <motion.div
          initial={{ opacity: 0 }}
          whileInView={{ opacity: 1 }}
          viewport={{ once: true }}
          transition={{ duration: 0.5, delay: number * 0.1 + 0.6 }}
          className={`absolute ${
            position === 'left'
              ? 'right-[-50%] top-1/2 transform -translate-y-1/2 rotate-0 hidden md:block'
              : position === 'right'
                ? 'left-[-50%] top-1/2 transform -translate-y-1/2 rotate-180 hidden md:block'
                : 'bottom-[-40px] left-1/2 transform -translate-x-1/2 rotate-90'
          }`}
        >
          <svg width="120" height="40" viewBox="0 0 120 40" fill="none" xmlns="http://www.w3.org/2000/svg">
            <motion.path
              d="M0 20H100"
              stroke="#00FF9D"
              strokeWidth="2"
              strokeDasharray="5 5"
              initial={{ pathLength: 0, opacity: 0 }}
              whileInView={{ pathLength: 1, opacity: 0.6 }}
              viewport={{ once: true }}
              transition={{ duration: 1, delay: number * 0.1 + 0.7 }}
            />
            <motion.path
              d="M100 20L110 10M100 20L110 30"
              stroke="#00FF9D"
              strokeWidth="2"
              initial={{ pathLength: 0, opacity: 0 }}
              whileInView={{ pathLength: 1, opacity: 1 }}
              viewport={{ once: true }}
              transition={{ duration: 0.5, delay: number * 0.1 + 1 }}
            />
          </svg>
        </motion.div>
      )}

      {/* Mobile-only vertical connector */}
      {!isLast && position === 'center' && (
        <motion.div
          initial={{ opacity: 0, height: 0 }}
          whileInView={{ opacity: 1, height: 80 }}
          viewport={{ once: true }}
          transition={{ duration: 0.5, delay: number * 0.1 + 0.6 }}
          className="absolute bottom-[-80px] left-1/2 transform -translate-x-1/2 w-[2px] bg-neon-green/50 md:hidden"
        >
          <motion.div
            className="absolute bottom-0 left-1/2 transform -translate-x-1/2 w-0 h-0 border-l-[6px] border-l-transparent border-r-[6px] border-r-transparent border-t-[8px] border-t-neon-green"
            initial={{ opacity: 0 }}
            whileInView={{ opacity: 1 }}
            viewport={{ once: true }}
            transition={{ duration: 0.3, delay: number * 0.1 + 0.9 }}
          />
        </motion.div>
      )}
    </motion.div>
  );
};

const ProcessSection: React.FC = () => {
  const steps = [
    {
      icon: <Lightbulb />,
      title: "Idea & Conceptualization",
      description: "We begin by deeply understanding your vision, goals, and challenges to craft a tailored strategy that sets the direction for your project. Every great solution begins with a great idea."
    },
    {
      icon: <Search />,
      title: "Research & Discovery",
      description: "We conduct comprehensive research into your industry, competitors, and users. This helps us identify opportunities and create a blueprint that aligns with your business objectives."
    },
    {
      icon: <Layers />,
      title: "Wireframing & Prototyping",
      description: "Next, we build wireframes and interactive prototypes that showcase the app or website's core structure and functionality, giving you a glimpse of how your ideas will come to life."
    },
    {
      icon: <Palette />,
      title: "UI/UX Design",
      description: "Our designers then craft a beautiful, functional UI with a focus on user experience. We integrate the latest design trends while ensuring accessibility and usability."
    },
    {
      icon: <Code />,
      title: "Frontend Development",
      description: "Using modern frameworks like React and Next.js, we ensure a smooth, responsive experience across all devices, with pixel-perfect implementation of designs."
    },
    {
      icon: <Server />,
      title: "Backend Development",
      description: "We implement scalable, secure server-side code, API integrations, and efficient database management to ensure the application runs flawlessly at scale."
    },
    {
      icon: <Link />,
      title: "Integration",
      description: "We connect all components, third-party services, and APIs into a cohesive system that works seamlessly together."
    },
    {
      icon: <Bug />,
      title: "Quality Assurance",
      description: "Every aspect of your project is rigorously tested to ensure it meets our high-quality standards. We test for bugs, performance issues, and ensure full compatibility across devices."
    },
    {
      icon: <Rocket />,
      title: "Launch & Support",
      description: "We deploy your project with precision and provide continuous maintenance, updates, and optimization to keep your project at peak performance as your business evolves."
    }
  ];

  // Function to determine position based on index
  const getPosition = (index: number): 'left' | 'right' | 'center' => {
    // For desktop, alternate between left and right
    // Mobile handling is done via CSS media queries
    return index % 2 === 0 ? 'left' : 'right';
  };

  // Use React's useEffect to handle responsive behavior
  const [isMobile, setIsMobile] = React.useState(false);

  React.useEffect(() => {
    const checkMobile = () => {
      setIsMobile(window.innerWidth < 768);
    };

    // Initial check
    checkMobile();

    // Add event listener for window resize
    window.addEventListener('resize', checkMobile);

    // Cleanup
    return () => window.removeEventListener('resize', checkMobile);
  }, []);

  return (
    <section id="process" className="py-24 grid-bg relative overflow-hidden">
      {/* Background elements */}
      <div className="absolute inset-0 -z-10">
        <div className="absolute top-1/4 left-1/4 w-64 h-64 bg-neon-green/5 rounded-full filter blur-[80px]"></div>
        <div className="absolute bottom-1/4 right-1/4 w-64 h-64 bg-neon-green/5 rounded-full filter blur-[80px]"></div>
      </div>

      <div className="container mx-auto px-4">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.5 }}
          className="max-w-4xl mx-auto mb-20 text-center"
        >
          <h2 className="text-3xl md:text-4xl font-bold mb-6 text-center">
            Our Proven{" "}
            <span className="bg-clip-text text-transparent bg-gradient-to-r from-neon-green to-neon-green/70 relative">
              9-Step Process
              <motion.div
                className="absolute -bottom-1 left-0 h-[3px] bg-gradient-to-r from-neon-green to-neon-green/70 rounded-full"
                initial={{ width: 0 }}
                whileInView={{ width: "100%" }}
                transition={{ duration: 0.5, delay: 0.2 }}
              />
            </span>
          </h2>
          <p className="text-gray-300 text-lg md:text-xl text-center max-w-3xl mx-auto leading-relaxed">
            At Nerdlab, we follow a precise, well-defined 9-step process that ensures your project is not only successful but exceeds expectations.
            Our method is built on years of experience, consistent results, and a commitment to delivering perfection every time.
          </p>
        </motion.div>

        {/* Flow Chart Container */}
        <div className="max-w-6xl mx-auto relative p-4 md:p-8 rounded-2xl bg-black/20 border border-neon-green/10 backdrop-blur-sm">
          {/* Flow Chart Title */}
          <div className="absolute -top-4 left-1/2 transform -translate-x-1/2 bg-black/80 px-4 py-1 rounded-full border border-neon-green/30 text-neon-green text-sm font-mono">
            FLOW CHART
          </div>

          {/* Main Flow Path - Visible only on larger screens */}
          <div className="hidden md:block absolute left-1/2 top-0 bottom-0 w-1 bg-gradient-to-b from-transparent via-neon-green/50 to-transparent -z-1"></div>

          {/* Process Steps */}
          <div className="relative z-10 py-8 md:py-16">
            {steps.map((step, index) => (
              <ProcessStep
                key={index}
                number={index + 1}
                title={step.title}
                description={step.description}
                icon={step.icon}
                position={isMobile ? 'center' : getPosition(index)}
                isLast={index === steps.length - 1}
              />
            ))}
          </div>
        </div>
      </div>
    </section>
  );
};

export default ProcessSection;
