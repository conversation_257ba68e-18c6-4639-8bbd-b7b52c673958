"use client"

import { motion, useInView } from "framer-motion"
import { useRef, useState } from "react"

function DinoGame() {
  const ref = useRef(null)
  const isInView = useInView(ref, { once: true })
  const [showGame, setShowGame] = useState(false)
  const [gameKey, setGameKey] = useState(0)

  const handleGameReset = () => {
    setGameKey(prev => prev + 1)
  }

  return (
    <section className="py-16 grid-bg">
      <div className="container mx-auto px-4">
        <div className="flex flex-col items-center text-center max-w-5xl mx-auto" ref={ref}>
          {/* Dino Game */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={isInView ? { opacity: 1, y: 0 } : {}}
            transition={{ duration: 0.5, delay: 0.3 }}
            className="w-full max-w-4xl mx-auto"
          >
            {!showGame ? (
              <div
                onClick={() => setShowGame(true)}
                className="bg-black/40 border border-neon-green/30 rounded-lg p-8 w-full overflow-hidden cursor-pointer hover:border-neon-green/60 transition-all duration-300 group"
              >
                <div className="text-neon-green text-left flex items-center gap-4">
                  <span className="text-2xl">🦖</span>
                  <span className="font-fira">Click to play the Chrome Dino Game</span>
                </div>
              </div>
            ) : (
              <div className="bg-black/40 border border-neon-green/30 rounded-lg p-8 w-full overflow-hidden">
                <div className="relative">
                  <iframe
                    key={gameKey}
                    src="https://chromedino.com/black/"
                    className="w-full h-[300px] rounded-lg"
                    style={{ filter: 'hue-rotate(100deg) brightness(1.2)' }}
                    onLoad={(e) => {
                      const iframe = e.target as HTMLIFrameElement;
                      if (iframe.contentWindow) {
                        const style = document.createElement('style');
                        style.textContent = `
                          .offline { background: transparent !important; }
                          #main-message { display: none !important; }
                          #offline-resources { display: none !important; }
                          #messageBox {
                            background: rgba(0, 255, 157, 0.1) !important;
                            border: 1px solid rgba(0, 255, 157, 0.3) !important;
                            padding: 20px !important;
                            border-radius: 8px !important;
                          }
                          #messageBox h1 {
                            color: #00ff9d !important;
                            font-family: 'Fira Code', monospace !important;
                            margin-bottom: 10px !important;
                          }
                          #messageBox span {
                            color: white !important;
                            font-family: 'Fira Code', monospace !important;
                          }
                          #reload-button {
                            background: rgba(0, 255, 157, 0.2) !important;
                            border: 1px solid rgba(0, 255, 157, 0.5) !important;
                            color: #00ff9d !important;
                            padding: 8px 16px !important;
                            border-radius: 4px !important;
                            cursor: pointer !important;
                            transition: all 0.3s !important;
                          }
                          #reload-button:hover {
                            background: rgba(0, 255, 157, 0.3) !important;
                          }
                        `;
                        iframe.contentDocument?.head.appendChild(style);

                        const observer = new MutationObserver((mutations) => {
                          mutations.forEach((mutation) => {
                            if (mutation.target instanceof Element && mutation.target.id === 'messageBox') {
                              const messageBox = mutation.target as HTMLElement;
                              const heading = messageBox.querySelector('h1');
                              const text = messageBox.querySelector('span');
                              const button = messageBox.querySelector('#reload-button');

                              if (heading) heading.textContent = "Game Over?";
                              if (text) text.textContent = "Don't worry, Nerdlab got your back!";
                              if (button) button.textContent = "Continue with us";
                            }
                          });
                        });

                        observer.observe(iframe.contentDocument?.body || document.body, {
                          subtree: true,
                          childList: true
                        });
                      }
                    }}
                  ></iframe>
                  <div className="flex justify-between items-center mt-4">
                    <button
                      onClick={handleGameReset}
                      className="bg-black/60 border border-neon-green/30 rounded-md px-4 py-2 text-neon-green font-fira text-sm hover:bg-neon-green/10 transition-colors"
                    >
                      Reset Game
                    </button>
                    <button
                      onClick={() => setShowGame(false)}
                      className="bg-black/60 border border-neon-green/30 rounded-md px-4 py-2 text-neon-green font-fira text-sm hover:bg-neon-green/10 transition-colors"
                    >
                      Hide Game
                    </button>
                  </div>
                </div>
              </div>
            )}
          </motion.div>
        </div>
      </div>
    </section>
  )
}

export default DinoGame;
