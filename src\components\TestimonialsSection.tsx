import React from 'react';
import { motion } from 'framer-motion';
import { Carousel, CarouselContent, CarouselItem } from "@/components/ui/carousel";
import { Star, Quote, Award } from 'lucide-react';

interface Testimonial {
  id: string;
  name: string;
  position: string;
  company: string;
  content: string;
  rating: number;
  image?: string;
}

interface Partnership {
  id: string;
  company: string;
  logo?: string;
  description?: string;
}

const TestimonialsSection: React.FC = () => {
  // Real client partnerships data using actual logos from public/client/
  const partnerships: Partnership[] = [
    {
      id: "1",
      company: "BoctoCrop",
      logo: "/client/BoctoCrop.png",
      description: "Agricultural technology solutions"
    },
    {
      id: "2",
      company: "ByteSlack",
      logo: "/client/ByteSlack.png",
      description: "Communication and collaboration platform"
    },
    {
      id: "3",
      company: "College Mastermind",
      logo: "/client/CollegeMastermind.png",
      description: "Educational consulting and guidance"
    },
    {
      id: "4",
      company: "Creative Centralian",
      logo: "/client/CreativeCentralian.png",
      description: "Creative design and marketing agency"
    },
    {
      id: "5",
      company: "Digest Knowledge",
      logo: "/client/DigestKnowedge .png",
      description: "Knowledge management platform"
    },
    {
      id: "6",
      company: "EcoMart",
      logo: "/client/EcoMart.png",
      description: "Sustainable e-commerce marketplace"
    },
    {
      id: "7",
      company: "LNP",
      logo: "/client/LNP.png",
      description: "Logistics and supply chain solutions"
    },
    {
      id: "8",
      company: "Micratto",
      logo: "/client/Micratto.png",
      description: "Micro-finance and payment solutions"
    },
    {
      id: "9",
      company: "Middler",
      logo: "/client/Middler.png",
      description: "Middleware and integration services"
    },
    {
      id: "10",
      company: "PetSquad TV",
      logo: "/client/PetSquadTV.png",
      description: "Pet entertainment and streaming platform"
    },
    {
      id: "11",
      company: "Photoreviser",
      logo: "/client/Photoreviser.png",
      description: "Photo editing and enhancement tools"
    },
    {
      id: "12",
      company: "Review Sensical",
      logo: "/client/ReviewSensical.png",
      description: "Review management and analytics"
    },
    {
      id: "13",
      company: "Ultra Engineering",
      logo: "/client/Ultra Engineering.png",
      description: "Engineering and construction solutions"
    }
  ];

  const testimonials: Testimonial[] = [
    {
      id: "1",
      name: "Jessica Chen",
      position: "CEO",
      company: "TechVision Inc.",
      content: "Working with NerdLab was a game-changer for our business. Their team delivered a complex platform that exceeded our expectations and helped us scale our operations significantly.",
      rating: 5,
      image: "https://images.unsplash.com/photo-1494790108377-be9c29b29330?ixlib=rb-1.2.1&auto=format&fit=crop&w=256&q=80"
    },
    {
      id: "2",
      name: "Marcus Williams",
      position: "CTO",
      company: "FinEdge Solutions",
      content: "The team at NerdLab brought technical excellence and creative thinking to our project. Their solution was elegant, scalable, and delivered on time and on budget.",
      rating: 5,
      image: "https://images.unsplash.com/photo-1500648767791-00dcc994a43e?ixlib=rb-1.2.1&auto=format&fit=crop&w=256&q=80"
    },
    {
      id: "3",
      name: "Sophia Rodriguez",
      position: "Founder",
      company: "HealthTech Innovations",
      content: "From the initial meeting to the project delivery, NerdLab exceeded our expectations. Their developers' technical skills and attention to detail are truly impressive.",
      rating: 5,
      image: "https://images.unsplash.com/photo-1580489944761-15a19d654956?ixlib=rb-1.2.1&auto=format&fit=crop&w=256&q=80"
    },
    {
      id: "4",
      name: "Alexander Kim",
      position: "Product Manager",
      company: "RetailPro",
      content: "NerdLab brought our vision to life with precision and creativity. Their collaborative approach ensured that every feature aligned perfectly with our business goals.",
      rating: 4,
      image: "https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?ixlib=rb-1.2.1&auto=format&fit=crop&w=256&q=80"
    },
    {
      id: "5",
      name: "Emma Watson",
      position: "Marketing Director",
      company: "GlobalBrand Solutions",
      content: "The e-commerce platform developed by NerdLab has significantly increased our conversion rates and user engagement. Their team truly understood our business needs.",
      rating: 5,
      image: "https://images.unsplash.com/photo-1534528741775-53994a69daeb?ixlib=rb-1.2.1&auto=format&fit=crop&w=256&q=80"
    },
    {
      id: "6",
      name: "David Park",
      position: "Operations Manager",
      company: "LogiTech Services",
      content: "NerdLab's custom logistics solution streamlined our operations and reduced processing time by 40%. Their technical expertise and business acumen are outstanding.",
      rating: 5,
      image: "https://images.unsplash.com/photo-1539571696357-5a69c17a67c6?ixlib=rb-1.2.1&auto=format&fit=crop&w=256&q=80"
    },
    {
      id: "7",
      name: "Olivia Martinez",
      position: "VP of Innovation",
      company: "NextGen Startups",
      content: "Working with NerdLab was a seamless experience. Their team's ability to translate complex requirements into elegant solutions sets them apart from other development agencies.",
      rating: 5,
      image: "https://images.unsplash.com/photo-1524250502761-1ac6f2e30d43?ixlib=rb-1.2.1&auto=format&fit=crop&w=256&q=80"
    },
    {
      id: "8",
      name: "James Wilson",
      position: "Director",
      company: "EducateNow",
      content: "Our learning management system developed by NerdLab has been transformative for our organization. The intuitive interface and robust functionality have received rave reviews from users.",
      rating: 4,
      image: "https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?ixlib=rb-1.2.1&auto=format&fit=crop&w=256&q=80"
    }
  ];

  const renderStars = (rating: number) => {
    return Array(5).fill(0).map((_, index) => (
      <Star
        key={index}
        className={`w-4 h-4 ${index < rating ? 'text-yellow-400 fill-yellow-400' : 'text-gray-400'}`}
      />
    ));
  };

  return (
    <section id="testimonials" className="py-24 relative overflow-hidden grid-bg">
      {/* Grid background */}

      {/* Background glow effects */}
      <div className="absolute inset-0 pointer-events-none">
        <div className="absolute top-1/4 right-1/4 w-96 h-96 bg-neon-green/5 rounded-full filter blur-[100px] animate-pulse"></div>
        <div className="absolute bottom-1/4 left-1/4 w-96 h-96 bg-neon-green/5 rounded-full filter blur-[100px] animate-pulse"></div>
      </div>

      {/* Partnerships Section */}
      <div className="container mx-auto px-4 mb-20 relative z-10">
        <div className="text-center mb-16">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.6 }}
          >
            <h2 className="text-3xl md:text-4xl font-bold mb-8">
              We are{" "}
              <span className="bg-clip-text text-transparent bg-gradient-to-r from-neon-green to-neon-green/70 relative">
                trusted by
                <motion.div
                  className="absolute -bottom-1 left-0 h-[3px] bg-gradient-to-r from-neon-green to-neon-green/70 rounded-full"
                  initial={{ width: 0 }}
                  whileInView={{ width: "100%" }}
                  transition={{ duration: 0.5, delay: 0.2 }}
                />
              </span>
            </h2>
          </motion.div>
        </div>

        {/* Partnerships Grid */}
        <div className="relative">
          <div className="absolute left-0 top-0 w-32 h-full bg-gradient-to-r from-black via-black/80 to-transparent z-10"></div>
          <div className="absolute right-0 top-0 w-32 h-full bg-gradient-to-l from-black via-black/80 to-transparent z-10"></div>

          <Carousel className="w-full" opts={{ loop: true, align: "start" }}>
            <CarouselContent className="animate-infinite-scroll -ml-4">
              {[...partnerships, ...partnerships].map((partnership, index) => (
                <CarouselItem key={`${partnership.id}-${index}`} className="basis-1/2 md:basis-1/3 lg:basis-1/4 xl:basis-1/5 pl-4">
                  <motion.div
                    className="flex flex-col items-center justify-center py-6 px-2 group text-center"
                    whileHover={{ scale: 1.05 }}
                    transition={{ duration: 0.3 }}
                  >
                    {partnership.logo && (
                      <div className="w-20 h-16 flex items-center justify-center mb-4">
                        <img
                          src={partnership.logo}
                          alt={`${partnership.company} logo`}
                          className="max-w-full max-h-full object-contain transition-all duration-300 filter brightness-100 group-hover:brightness-110"
                        />
                      </div>
                    )}

                    <div className="space-y-2">
                      <h3 className="text-neon-green font-semibold text-lg group-hover:text-neon-green/80 transition-colors duration-300">
                        {partnership.company}
                      </h3>
                      {partnership.description && (
                        <p className="text-gray-400 text-sm leading-relaxed max-w-[200px]">
                          {partnership.description}
                        </p>
                      )}
                    </div>
                  </motion.div>
                </CarouselItem>
              ))}
            </CarouselContent>
          </Carousel>
        </div>
      </div>

      {/* Testimonials Section */}
      <div className="container mx-auto px-4 mb-12 relative z-10">
        <div className="text-center mb-16">
          <h2 className="text-3xl md:text-4xl font-bold mb-6">
            Reviews From Our{" "}
            <span className="bg-clip-text text-transparent bg-gradient-to-r from-neon-green to-neon-green/70 relative">
              Clients
              <motion.div
                className="absolute -bottom-1 left-0 h-[3px] bg-gradient-to-r from-neon-green to-neon-green/70 rounded-full"
                initial={{ width: 0 }}
                whileInView={{ width: "100%" }}
                transition={{ duration: 0.5, delay: 0.2 }}
              />
            </span>
          </h2>
        </div>

        <div className="relative">
          <div className="absolute left-0 top-0 w-32 h-full bg-gradient-to-r from-black via-black/80 to-transparent z-10"></div>
          <div className="absolute right-0 top-0 w-32 h-full bg-gradient-to-l from-black via-black/80 to-transparent z-10"></div>

          <Carousel className="w-full" opts={{ loop: true, align: "start" }}>
            <CarouselContent className="animate-infinite-scroll">
              {[...testimonials, ...testimonials].map((testimonial, index) => (
                <CarouselItem key={`${testimonial.id}-${index}`} className="md:basis-1/3 lg:basis-1/3 pl-4">
                  <div className="bg-black/40 backdrop-blur-lg border border-neon-green/10 p-6 rounded-lg h-full flex flex-col relative group hover:border-neon-green/30 transition-all duration-500">
                    <Quote className="absolute top-3 right-3 text-neon-green/20 w-8 h-8" />

                    <div className="mb-4 flex">
                      {renderStars(testimonial.rating)}
                    </div>

                    <p className="text-gray-300 mb-4 flex-grow">{testimonial.content}</p>

                    <div className="flex items-center mt-auto">
                      {testimonial.image && (
                        <div className="w-10 h-10 rounded-full overflow-hidden mr-3 ring-2 ring-neon-green/20">
                          <img
                            src={testimonial.image}
                            alt={testimonial.name}
                            className="w-full h-full object-cover"
                          />
                        </div>
                      )}

                      <div>
                        <p className="font-medium text-white">{testimonial.name}</p>
                        <p className="text-neon-green text-sm">{testimonial.position}, {testimonial.company}</p>
                      </div>
                    </div>
                  </div>
                </CarouselItem>
              ))}
            </CarouselContent>
          </Carousel>
        </div>
      </div>
    </section>
  );
};

export default TestimonialsSection;
