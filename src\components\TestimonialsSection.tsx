import React from 'react';
import { motion } from 'framer-motion';
import { Carousel, CarouselContent, CarouselItem } from "@/components/ui/carousel";
import { Star, Quote, Award } from 'lucide-react';

interface Testimonial {
  id: string;
  name: string;
  position: string;
  company: string;
  content: string;
  rating: number;
  image?: string;
}

interface Partnership {
  id: string;
  company: string;
  logo?: string;
  description?: string;
}

const TestimonialsSection: React.FC = () => {
  // Partnership data extracted from testimonials
  const partnerships: Partnership[] = [
    {
      id: "1",
      company: "TechVision Inc.",
      logo: "https://images.unsplash.com/photo-**********-b33ff0c44a43?ixlib=rb-1.2.1&auto=format&fit=crop&w=256&q=80",
      description: "Leading technology solutions provider"
    },
    {
      id: "2",
      company: "FinEdge Solutions",
      logo: "https://images.unsplash.com/photo-**********-e076c223a692?ixlib=rb-1.2.1&auto=format&fit=crop&w=256&q=80",
      description: "Financial technology innovators"
    },
    {
      id: "3",
      company: "DataFlow Systems",
      logo: "https://images.unsplash.com/photo-**********-bebda4e38f71?ixlib=rb-1.2.1&auto=format&fit=crop&w=256&q=80",
      description: "Data analytics and automation"
    },
    {
      id: "4",
      company: "CloudNest",
      logo: "https://images.unsplash.com/photo-1573164713714-d95e436ab8d6?ixlib=rb-1.2.1&auto=format&fit=crop&w=256&q=80",
      description: "Cloud infrastructure specialists"
    },
    {
      id: "5",
      company: "InnovateLab",
      logo: "https://images.unsplash.com/photo-**********-d307ca884978?ixlib=rb-1.2.1&auto=format&fit=crop&w=256&q=80",
      description: "Innovation and research lab"
    },
    {
      id: "6",
      company: "EducateNow",
      logo: "https://images.unsplash.com/photo-1497486751825-1233686d5d80?ixlib=rb-1.2.1&auto=format&fit=crop&w=256&q=80",
      description: "Educational technology platform"
    },
    {
      id: "7",
      company: "RetailMax",
      logo: "https://images.unsplash.com/photo-**********-0cfed4f6a45d?ixlib=rb-1.2.1&auto=format&fit=crop&w=256&q=80",
      description: "E-commerce solutions"
    },
    {
      id: "8",
      company: "HealthTech Pro",
      logo: "https://images.unsplash.com/photo-1576091160399-112ba8d25d1f?ixlib=rb-1.2.1&auto=format&fit=crop&w=256&q=80",
      description: "Healthcare technology solutions"
    }
  ];

  const testimonials: Testimonial[] = [
    {
      id: "1",
      name: "Jessica Chen",
      position: "CEO",
      company: "TechVision Inc.",
      content: "Working with NerdLab was a game-changer for our business. Their team delivered a complex platform that exceeded our expectations and helped us scale our operations significantly.",
      rating: 5,
      image: "https://images.unsplash.com/photo-1494790108377-be9c29b29330?ixlib=rb-1.2.1&auto=format&fit=crop&w=256&q=80"
    },
    {
      id: "2",
      name: "Marcus Williams",
      position: "CTO",
      company: "FinEdge Solutions",
      content: "The team at NerdLab brought technical excellence and creative thinking to our project. Their solution was elegant, scalable, and delivered on time and on budget.",
      rating: 5,
      image: "https://images.unsplash.com/photo-1500648767791-00dcc994a43e?ixlib=rb-1.2.1&auto=format&fit=crop&w=256&q=80"
    },
    {
      id: "3",
      name: "Sophia Rodriguez",
      position: "Founder",
      company: "HealthTech Innovations",
      content: "From the initial meeting to the project delivery, NerdLab exceeded our expectations. Their developers' technical skills and attention to detail are truly impressive.",
      rating: 5,
      image: "https://images.unsplash.com/photo-1580489944761-15a19d654956?ixlib=rb-1.2.1&auto=format&fit=crop&w=256&q=80"
    },
    {
      id: "4",
      name: "Alexander Kim",
      position: "Product Manager",
      company: "RetailPro",
      content: "NerdLab brought our vision to life with precision and creativity. Their collaborative approach ensured that every feature aligned perfectly with our business goals.",
      rating: 4,
      image: "https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?ixlib=rb-1.2.1&auto=format&fit=crop&w=256&q=80"
    },
    {
      id: "5",
      name: "Emma Watson",
      position: "Marketing Director",
      company: "GlobalBrand Solutions",
      content: "The e-commerce platform developed by NerdLab has significantly increased our conversion rates and user engagement. Their team truly understood our business needs.",
      rating: 5,
      image: "https://images.unsplash.com/photo-1534528741775-53994a69daeb?ixlib=rb-1.2.1&auto=format&fit=crop&w=256&q=80"
    },
    {
      id: "6",
      name: "David Park",
      position: "Operations Manager",
      company: "LogiTech Services",
      content: "NerdLab's custom logistics solution streamlined our operations and reduced processing time by 40%. Their technical expertise and business acumen are outstanding.",
      rating: 5,
      image: "https://images.unsplash.com/photo-1539571696357-5a69c17a67c6?ixlib=rb-1.2.1&auto=format&fit=crop&w=256&q=80"
    },
    {
      id: "7",
      name: "Olivia Martinez",
      position: "VP of Innovation",
      company: "NextGen Startups",
      content: "Working with NerdLab was a seamless experience. Their team's ability to translate complex requirements into elegant solutions sets them apart from other development agencies.",
      rating: 5,
      image: "https://images.unsplash.com/photo-1524250502761-1ac6f2e30d43?ixlib=rb-1.2.1&auto=format&fit=crop&w=256&q=80"
    },
    {
      id: "8",
      name: "James Wilson",
      position: "Director",
      company: "EducateNow",
      content: "Our learning management system developed by NerdLab has been transformative for our organization. The intuitive interface and robust functionality have received rave reviews from users.",
      rating: 4,
      image: "https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?ixlib=rb-1.2.1&auto=format&fit=crop&w=256&q=80"
    }
  ];

  const renderStars = (rating: number) => {
    return Array(5).fill(0).map((_, index) => (
      <Star
        key={index}
        className={`w-4 h-4 ${index < rating ? 'text-yellow-400 fill-yellow-400' : 'text-gray-400'}`}
      />
    ));
  };

  return (
    <section id="testimonials" className="py-24 relative overflow-hidden grid-bg">
      {/* Grid background */}

      {/* Background glow effects */}
      <div className="absolute inset-0 pointer-events-none">
        <div className="absolute top-1/4 right-1/4 w-96 h-96 bg-neon-green/5 rounded-full filter blur-[100px] animate-pulse"></div>
        <div className="absolute bottom-1/4 left-1/4 w-96 h-96 bg-neon-green/5 rounded-full filter blur-[100px] animate-pulse"></div>
      </div>

      {/* Partnerships Section */}
      <div className="container mx-auto px-4 mb-20 relative z-10">
        <div className="text-center mb-16">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.6 }}
          >
            <h2 className="text-3xl md:text-4xl font-bold mb-6">
              Partnerships We Are{" "}
              <span className="bg-clip-text text-transparent bg-gradient-to-r from-neon-green to-neon-green/70 relative">
                Proud Of
                <motion.div
                  className="absolute -bottom-1 left-0 h-[3px] bg-gradient-to-r from-neon-green to-neon-green/70 rounded-full"
                  initial={{ width: 0 }}
                  whileInView={{ width: "100%" }}
                  transition={{ duration: 0.5, delay: 0.2 }}
                />
              </span>
            </h2>
            <p className="text-gray-400 max-w-2xl mx-auto">
              Trusted by innovative companies across industries to deliver exceptional digital solutions
            </p>
          </motion.div>
        </div>

        {/* Partnerships Grid */}
        <div className="relative">
          <div className="absolute left-0 top-0 w-32 h-full bg-gradient-to-r from-black via-black/80 to-transparent z-10"></div>
          <div className="absolute right-0 top-0 w-32 h-full bg-gradient-to-l from-black via-black/80 to-transparent z-10"></div>

          <Carousel className="w-full" opts={{ loop: true, align: "start" }}>
            <CarouselContent className="animate-infinite-scroll">
              {[...partnerships, ...partnerships].map((partnership, index) => (
                <CarouselItem key={`${partnership.id}-${index}`} className="md:basis-1/4 lg:basis-1/5 pl-4">
                  <motion.div
                    className="bg-black/40 backdrop-blur-lg border border-neon-green/10 p-6 rounded-lg h-full flex flex-col items-center justify-center relative group hover:border-neon-green/30 transition-all duration-500 hover:shadow-[0_0_30px_rgba(0,255,157,0.1)]"
                    whileHover={{ scale: 1.05 }}
                    transition={{ duration: 0.2 }}
                  >
                    <Award className="absolute top-3 right-3 text-neon-green/20 w-6 h-6" />

                    {partnership.logo && (
                      <div className="w-16 h-16 rounded-full overflow-hidden mb-4 ring-2 ring-neon-green/20 group-hover:ring-neon-green/40 transition-all duration-300">
                        <img
                          src={partnership.logo}
                          alt={`${partnership.company} logo`}
                          className="w-full h-full object-cover grayscale group-hover:grayscale-0 transition-all duration-300"
                        />
                      </div>
                    )}

                    <div className="text-center">
                      <h3 className="font-semibold text-white mb-2 group-hover:text-neon-green transition-colors duration-300">
                        {partnership.company}
                      </h3>
                      {partnership.description && (
                        <p className="text-gray-400 text-sm">
                          {partnership.description}
                        </p>
                      )}
                    </div>
                  </motion.div>
                </CarouselItem>
              ))}
            </CarouselContent>
          </Carousel>
        </div>
      </div>

      {/* Testimonials Section */}
      <div className="container mx-auto px-4 mb-12 relative z-10">
        <div className="text-center mb-16">
          <h2 className="text-3xl md:text-4xl font-bold mb-6">
            Reviews From Our{" "}
            <span className="bg-clip-text text-transparent bg-gradient-to-r from-neon-green to-neon-green/70 relative">
              Clients
              <motion.div
                className="absolute -bottom-1 left-0 h-[3px] bg-gradient-to-r from-neon-green to-neon-green/70 rounded-full"
                initial={{ width: 0 }}
                whileInView={{ width: "100%" }}
                transition={{ duration: 0.5, delay: 0.2 }}
              />
            </span>
          </h2>
        </div>

        <div className="relative">
          <div className="absolute left-0 top-0 w-32 h-full bg-gradient-to-r from-black via-black/80 to-transparent z-10"></div>
          <div className="absolute right-0 top-0 w-32 h-full bg-gradient-to-l from-black via-black/80 to-transparent z-10"></div>

          <Carousel className="w-full" opts={{ loop: true, align: "start" }}>
            <CarouselContent className="animate-infinite-scroll">
              {[...testimonials, ...testimonials].map((testimonial, index) => (
                <CarouselItem key={`${testimonial.id}-${index}`} className="md:basis-1/3 lg:basis-1/3 pl-4">
                  <div className="bg-black/40 backdrop-blur-lg border border-neon-green/10 p-6 rounded-lg h-full flex flex-col relative group hover:border-neon-green/30 transition-all duration-500">
                    <Quote className="absolute top-3 right-3 text-neon-green/20 w-8 h-8" />

                    <div className="mb-4 flex">
                      {renderStars(testimonial.rating)}
                    </div>

                    <p className="text-gray-300 mb-4 flex-grow">{testimonial.content}</p>

                    <div className="flex items-center mt-auto">
                      {testimonial.image && (
                        <div className="w-10 h-10 rounded-full overflow-hidden mr-3 ring-2 ring-neon-green/20">
                          <img
                            src={testimonial.image}
                            alt={testimonial.name}
                            className="w-full h-full object-cover"
                          />
                        </div>
                      )}

                      <div>
                        <p className="font-medium text-white">{testimonial.name}</p>
                        <p className="text-neon-green text-sm">{testimonial.position}, {testimonial.company}</p>
                      </div>
                    </div>
                  </div>
                </CarouselItem>
              ))}
            </CarouselContent>
          </Carousel>
        </div>
      </div>
    </section>
  );
};

export default TestimonialsSection;
