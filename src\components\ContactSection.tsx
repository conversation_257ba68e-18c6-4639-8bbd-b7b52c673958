
import React, { useState } from 'react';
import { toast } from 'sonner';
import { Upload } from 'lucide-react';
import { motion } from 'framer-motion';
import { Textarea } from "@/components/ui/textarea";

const ContactSection: React.FC = () => {
  const [projectName, setProjectName] = useState('');
  const [projectIdea, setProjectIdea] = useState('');
  const [name, setName] = useState('');
  const [email, setEmail] = useState('');
  const [file, setFile] = useState<File | null>(null);

  const [services, setServices] = useState({
    uiux: false,
    web: false,
    app: false,
    ai: false,
    computerVision: false,
    ar: false,
    branding: false,
    other: false
  });

  const handleServiceToggle = (service: keyof typeof services) => {
    setServices(prev => ({
      ...prev,
      [service]: !prev[service]
    }));
  };

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files[0]) {
      setFile(e.target.files[0]);
    }
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();

    // Basic validation
    if (!projectName || !projectIdea || !name || !email) {
      toast.error('Please fill out all required fields');
      return;
    }

    // In a real app, you would send this data to your backend
    console.log({
      projectName,
      projectIdea,
      services,
      file,
      name,
      email
    });

    toast.success('Got your idea! We\'ll get back to you soon.');

    // Reset form
    setProjectName('');
    setProjectIdea('');
    setServices({
      uiux: false,
      web: false,
      app: false,
      ai: false,
      computerVision: false,
      ar: false,
      branding: false,
      other: false
    });
    setFile(null);
    setName('');
    setEmail('');
  };

  return (
    <section id="contact" className="py-24 grid-bg">
      <div className="container mx-auto px-4 mb-12 relative z-10">
          <div className="max-w-4xl mx-auto text-center">
            <h2 className="text-3xl md:text-4xl font-bold mb-6 text-white">
              Ready to bring your{" "}
              <span className="bg-clip-text text-transparent bg-gradient-to-r from-neon-green to-neon-green/70 relative">
                vision
                <motion.div
                  className="absolute -bottom-1 left-0 h-[3px] bg-gradient-to-r from-neon-green to-neon-green/70 rounded-full"
                  initial={{ width: 0 }}
                  whileInView={{ width: "100%" }}
                  transition={{ duration: 0.5, delay: 0.2 }}
                />
              </span>{" "}
              to reality?
            </h2>
          </div>
        </div>
      <div className="container mx-auto px-4">

        <div className="max-w-3xl mx-auto bg-card rounded-xl p-6 md:p-8 border border-border glass-morphism">
          <div className="mb-8">
            <h2 className="text-3xl md:text-4xl font-bold mb-6 text-center">
              Get A{" "}
              <span className="bg-clip-text text-transparent bg-gradient-to-r from-neon-green to-neon-green/70 relative">
                Quote
                <motion.div
                  className="absolute -bottom-1 left-0 h-[3px] bg-gradient-to-r from-neon-green to-neon-green/70 rounded-full"
                  initial={{ width: 0 }}
                  whileInView={{ width: "100%" }}
                  transition={{ duration: 0.5, delay: 0.2 }}
                />
              </span>
            </h2>
          </div>

          <form onSubmit={handleSubmit} className="space-y-6">
            <div>
              <label className="block font-medium mb-2">
                <span className="flex items-center">
                  What's Your Project Called?
                  <span className="ml-2 text-sm text-gray-400">(Required)</span>
                </span>
              </label>
              <div className="relative">
                <input
                  type="text"
                  value={projectName}
                  onChange={(e) => setProjectName(e.target.value)}
                  placeholder="e.g., 'Space App 2.0'"
                  className="w-full bg-secondary px-4 py-3 rounded-md border border-border focus:outline-none focus:ring-1 focus:ring-neon-green"
                  required
                />
                <div className="absolute bottom-0 right-0 top-0 left-0 bg-gradient-to-r from-neon-green/5 to-transparent pointer-events-none rounded-md"></div>
              </div>
            </div>

            <div>
              <label className="block font-medium mb-2">
                <span className="flex items-center">
                  What's the Big Idea?
                  <span className="ml-2 text-sm text-gray-400">(Required)</span>
                </span>
              </label>
              <div className="relative">
                <Textarea
                  value={projectIdea}
                  onChange={(e) => setProjectIdea(e.target.value)}
                  placeholder="Tell us about your project..."
                  className="w-full min-h-[120px] bg-secondary px-4 py-3 rounded-md border border-border focus:outline-none focus:ring-1 focus:ring-neon-green"
                  required
                />
                <div className="absolute bottom-0 right-0 top-0 left-0 bg-gradient-to-r from-neon-green/5 to-transparent pointer-events-none rounded-md"></div>
              </div>
            </div>

            <div>
              <label className="block font-medium mb-2">
                What Do You Need Help With?
              </label>
              <div className="grid grid-cols-1 sm:grid-cols-2 gap-3">
                {[
                  { key: 'web', label: 'Website Development' },
                  { key: 'app', label: 'Mobile App Development' },
                  { key: 'uiux', label: 'UI/UX Design' },
                  { key: 'ar', label: '3D or AR/VR Experience' },
                  { key: 'ai', label: 'AI or ChatBot Integration' },
                  { key: 'other', label: 'Something Else' }
                ].map(({ key, label }) => (
                  <div className="relative" key={key}>
                    <label className="flex items-center space-x-2 p-3 rounded-md border border-border hover:border-neon-green/50 transition-colors cursor-pointer w-full">
                      <input
                        type="checkbox"
                        checked={services[key as keyof typeof services]}
                        onChange={() => handleServiceToggle(key as keyof typeof services)}
                        className="w-4 h-4 accent-neon-green"
                      />
                      <span>{label}</span>
                    </label>
                    <div className="absolute bottom-0 right-0 top-0 left-0 bg-gradient-to-r from-neon-green/5 to-transparent pointer-events-none rounded-md"></div>
                  </div>
                ))}
              </div>
            </div>

            <div>
              <label className="block font-medium mb-2">
                Want to Show Us Something?
              </label>
              <div className="relative">
                <div className="w-full bg-secondary px-4 py-3 rounded-md border border-border focus:outline-none focus:ring-1 focus:ring-neon-green flex items-center justify-between">
                  <span className="text-gray-400">{file ? file.name : "No file selected"}</span>
                  <label htmlFor="file-upload" className="cursor-pointer bg-gray-700 hover:bg-gray-600 text-white px-4 py-1 rounded-md flex items-center">
                    <Upload size={14} className="mr-1" />
                    Browse
                  </label>
                </div>
                <div className="absolute bottom-0 right-0 top-0 left-0 bg-gradient-to-r from-neon-green/5 to-transparent pointer-events-none rounded-md"></div>
                <input
                  id="file-upload"
                  type="file"
                  onChange={handleFileChange}
                  className="hidden"
                />
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="relative">
                <label className="block font-medium mb-2">
                  <span className="flex items-center">
                    Your Name
                    <span className="ml-2 text-sm text-gray-400">(Required)</span>
                  </span>
                </label>
                <input
                  type="text"
                  value={name}
                  onChange={(e) => setName(e.target.value)}
                  className="w-full bg-secondary px-4 py-3 rounded-md border border-border focus:outline-none focus:ring-1 focus:ring-neon-green"
                  required
                />
                <div className="absolute bottom-0 right-0 top-0 left-0 bg-gradient-to-r from-neon-green/5 to-transparent pointer-events-none rounded-md"></div>
              </div>

              <div className="relative">
                <label className="block font-medium mb-2">
                  <span className="flex items-center">
                    Email Address
                    <span className="ml-2 text-sm text-gray-400">(Required)</span>
                  </span>
                </label>
                <input
                  type="email"
                  value={email}
                  onChange={(e) => setEmail(e.target.value)}
                  className="w-full bg-secondary px-4 py-3 rounded-md border border-border focus:outline-none focus:ring-1 focus:ring-neon-green"
                  required
                />
                <div className="absolute bottom-0 right-0 top-0 left-0 bg-gradient-to-r from-neon-green/5 to-transparent pointer-events-none rounded-md"></div>
              </div>
            </div>

            <div className="pt-4">
              <button type="submit" className="neon-button group w-full justify-center py-4">
                <span className="relative flex items-center transition-transform duration-300 text-lg font-medium">
                  🚀 Launch My Idea
                </span>
              </button>
            </div>
          </form>
        </div>
      </div>
    </section>
  );
};

export default ContactSection;
